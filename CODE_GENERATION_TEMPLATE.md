# 数据接口代码生成模板

## 快速生成新接口的步骤

### 1. 数据记录类模板

```java
package com.deepinnet.localdata.integration.model.output;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.io.Serializable;

/**
 * Description: {接口名称}数据记录
 * Date: {当前日期}
 * Author: {作者}
 */
@Data
public class {接口名称}Record implements Serializable {

    private static final long serialVersionUID = 1L;

    // 根据实际JSON响应定义字段
    // 示例：
    // @JsonProperty("field_name")
    // private String fieldName;
    
    // TODO: 根据实际接口返回的JSON数据定义字段
}
```

### 2. 响应DTO模板

```java
package com.deepinnet.localdata.integration.model.output;

/**
 * Description: {接口名称}数据响应DTO
 * Date: {当前日期}
 * Author: {作者}
 */
public class {接口名称}ResponseDTO extends BaseDataResponseDTO<{接口名称}Record> {

    private static final long serialVersionUID = 1L;

    // 继承所有基类功能，无需额外代码
}
```

### 3. 客户端接口模板

```java
package com.deepinnet.localdata.integration;

import com.deepinnet.localdata.integration.config.GenerateDefaultClient;
import com.deepinnet.localdata.integration.model.input.BaseQueryDTO;
import com.deepinnet.localdata.integration.model.output.{接口名称}ResponseDTO;

/**
 * Description: {接口名称}数据查询客户端接口
 * Date: {当前日期}
 * Author: {作者}
 */
@GenerateDefaultClient
public interface {接口名称}Client {

    /**
     * 获取{接口名称}数据
     */
    {接口名称}ResponseDTO get{接口名称}Data(BaseQueryDTO queryDTO);

    /**
     * 获取{接口名称}数据（简化方法）
     */
    {接口名称}ResponseDTO get{接口名称}Data(String pageNo, String pageSize, String search);

    /**
     * 获取{接口名称}数据（最简化方法）
     */
    {接口名称}ResponseDTO get{接口名称}Data();
}
```

### 4. 客户端实现类模板

```java
package com.deepinnet.localdata.integration.impl;

import com.deepinnet.localdata.integration.{接口名称}Client;
import com.deepinnet.localdata.integration.constants.ActionConstants;
import com.deepinnet.localdata.integration.factory.DataClientFactory;
import com.deepinnet.localdata.integration.model.input.BaseQueryDTO;
import com.deepinnet.localdata.integration.model.output.{接口名称}ResponseDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Description: {接口名称}数据查询客户端实现类
 * Date: {当前日期}
 * Author: {作者}
 */
@Slf4j
@Service
public class {接口名称}ClientImpl extends DataClientFactory implements {接口名称}Client {

    /**
     * {接口名称}数据接口地址模板
     * TODO: 根据实际接口地址进行配置
     */
    private static final String {接口名称大写}_ACTION_TEMPLATE = 
            "/services/{数据库标识}/{接口路径}/" + ActionConstants.DA_PENG_TOKEN_PLACEHOLDER + "/getDataJson";

    @Override
    public {接口名称}ResponseDTO get{接口名称}Data(BaseQueryDTO queryDTO) {
        log.info("开始调用{接口名称}数据接口获取数据，参数: {}", queryDTO);
        return getData(queryDTO, {接口名称大写}_ACTION_TEMPLATE, {接口名称}ResponseDTO.class);
    }

    @Override
    public {接口名称}ResponseDTO get{接口名称}Data(String pageNo, String pageSize, String search) {
        return getData(pageNo, pageSize, search, {接口名称大写}_ACTION_TEMPLATE, {接口名称}ResponseDTO.class);
    }

    @Override
    public {接口名称}ResponseDTO get{接口名称}Data() {
        return getData({接口名称大写}_ACTION_TEMPLATE, {接口名称}ResponseDTO.class);
    }
}
```

### 5. 控制器模板

```java
package com.deepinnet.localdata.controller;

import com.deepinnet.localdata.integration.{接口名称}Client;
import com.deepinnet.localdata.integration.model.input.BaseQueryDTO;
import com.deepinnet.localdata.integration.model.output.{接口名称}ResponseDTO;
import com.deepinnet.localdata.integration.util.ResponseParseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * Description: {接口名称}数据接口测试控制器
 * Date: {当前日期}
 * Author: {作者}
 */
@Api(tags = "{接口名称}数据接口")
@RestController
@RequestMapping("/{接口路径}")
@Slf4j
public class {接口名称}Controller {

    @Resource
    private {接口名称}Client {接口名称小写}Client;

    @ApiOperation(value = "获取{接口名称}数据（默认）", notes = "使用默认参数获取{接口名称}数据")
    @GetMapping("/data")
    public {接口名称}ResponseDTO getData() {
        try {
            log.info("开始调用{接口名称}数据接口（默认参数）");
            
            {接口名称}ResponseDTO response = {接口名称小写}Client.get{接口名称}Data();
            
            if (ResponseParseUtil.isSuccess(response)) {
                log.info("成功获取{接口名称}数据，总记录数: {}", ResponseParseUtil.getTotalRecords(response));
            } else {
                log.warn("{接口名称}数据接口返回错误: {}", ResponseParseUtil.getErrorMessage(response));
            }
            
            return response;
            
        } catch (Exception e) {
            log.error("获取{接口名称}数据失败", e);
            throw e;
        }
    }

    @ApiOperation(value = "获取{接口名称}数据（分页）", notes = "使用指定分页参数获取{接口名称}数据")
    @GetMapping("/data/paged")
    public {接口名称}ResponseDTO getDataPaged(
            @ApiParam(value = "页号", example = "1") @RequestParam(defaultValue = "1") String pageNo,
            @ApiParam(value = "页面大小", example = "20") @RequestParam(defaultValue = "20") String pageSize,
            @ApiParam(value = "搜索条件JSON", example = "{}") @RequestParam(defaultValue = "{}") String search) {
        
        try {
            log.info("开始调用{接口名称}数据接口（分页参数），pageNo: {}, pageSize: {}, search: {}", 
                    pageNo, pageSize, search);
            
            {接口名称}ResponseDTO response = {接口名称小写}Client.get{接口名称}Data(pageNo, pageSize, search);
            
            if (ResponseParseUtil.isSuccess(response)) {
                log.info("成功获取{接口名称}数据，总记录数: {}", ResponseParseUtil.getTotalRecords(response));
            } else {
                log.warn("{接口名称}数据接口返回错误: {}", ResponseParseUtil.getErrorMessage(response));
            }
            
            return response;
            
        } catch (Exception e) {
            log.error("获取{接口名称}数据失败", e);
            throw e;
        }
    }

    @ApiOperation(value = "获取{接口名称}数据（POST）", notes = "使用POST方式提交查询参数")
    @PostMapping("/data")
    public {接口名称}ResponseDTO getDataByPost(@RequestBody BaseQueryDTO queryDTO) {
        try {
            log.info("开始调用{接口名称}数据接口（POST方式），参数: {}", queryDTO);
            
            {接口名称}ResponseDTO response = {接口名称小写}Client.get{接口名称}Data(queryDTO);
            
            if (ResponseParseUtil.isSuccess(response)) {
                log.info("成功获取{接口名称}数据，总记录数: {}", ResponseParseUtil.getTotalRecords(response));
            } else {
                log.warn("{接口名称}数据接口返回错误: {}", ResponseParseUtil.getErrorMessage(response));
            }
            
            return response;
            
        } catch (Exception e) {
            log.error("获取{接口名称}数据失败", e);
            throw e;
        }
    }

    @ApiOperation(value = "测试{接口名称}数据接口", notes = "测试{接口名称}数据接口的连通性和基本功能")
    @GetMapping("/test")
    public String testConnection() {
        try {
            log.info("开始测试{接口名称}数据接口连通性");
            
            {接口名称}ResponseDTO response = {接口名称小写}Client.get{接口名称}Data("1", "1", "");
            
            if (ResponseParseUtil.isSuccess(response)) {
                return "{接口名称}数据接口连通性测试成功！总记录数: " + ResponseParseUtil.getTotalRecords(response);
            } else {
                return "{接口名称}数据接口连通性测试失败: " + ResponseParseUtil.getErrorMessage(response);
            }
            
        } catch (Exception e) {
            log.error("{接口名称}数据接口连通性测试异常", e);
            return "{接口名称}数据接口连通性测试异常: " + e.getMessage();
        }
    }
}
```

### 6. 注册Bean

在`GenerateDefaultClientBeanFactoryPostProcess.java`中添加：

```java
checkAndCreateDefaultProxy(beanFactory, "com.deepinnet.localdata.integration.{接口名称}Client");
```

## 替换占位符说明

创建新接口时，需要替换以下占位符：

- `{接口名称}` - 接口名称，如：ShipData、VehicleInfo等
- `{接口名称大写}` - 接口名称大写，如：SHIP_DATA、VEHICLE_INFO等
- `{接口名称小写}` - 接口名称小写，如：shipData、vehicleInfo等
- `{接口路径}` - URL路径，如：ship-data、vehicle-info等
- `{数据库标识}` - 数据库标识，如：DYWHSJ_DB_3等
- `{当前日期}` - 当前日期，如：2025/9/8
- `{作者}` - 作者名称

## 示例：创建车辆信息接口

假设要创建一个车辆信息接口，替换后的文件名和类名如下：

- `VehicleInfoRecord.java`
- `VehicleInfoResponseDTO.java`
- `VehicleInfoClient.java`
- `VehicleInfoClientImpl.java`
- `VehicleInfoController.java`

这样就可以快速创建一个完整的数据接口，大大提高开发效率。
