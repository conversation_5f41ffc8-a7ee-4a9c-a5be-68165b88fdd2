# 代码生成模板

本文档提供了基于通用响应结构快速生成新接口代码的模板。

## 模板变量说明

在使用模板时，需要替换以下变量：

- `{DataType}`: 数据类型名称（如：Vehicle、Device、User等）
- `{dataType}`: 数据类型名称（首字母小写）
- `{DATA_TYPE}`: 数据类型名称（全大写，用于常量）
- `{description}`: 数据类型的中文描述
- `{apiPath}`: API接口路径
- `{requestMapping}`: 控制器请求映射路径

## 1. 数据记录类模板

文件路径: `sz-data-integration/src/main/java/com/deepinnet/localdata/integration/model/output/{DataType}DataRecord.java`

```java
package com.deepinnet.localdata.integration.model.output;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Description: {description}数据记录
 * Date: 2025/9/8
 * Author: qoder
 */
@Data
public class {DataType}DataRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @JsonProperty("id")
    private String id;

    /**
     * 名称
     */
    @JsonProperty("name")
    private String name;

    /**
     * 状态
     */
    @JsonProperty("status")
    private String status;

    /**
     * 创建时间
     */
    @JsonProperty("createtime")
    private String createTime;

    /**
     * 更新时间
     */
    @JsonProperty("updatetime")
    private String updateTime;

    // TODO: 根据实际API响应添加更多字段
    // 参考格式：
    // @JsonProperty("json_field_name")
    // private String javaFieldName;
}
```

## 2. 响应DTO模板

文件路径: `sz-data-integration/src/main/java/com/deepinnet/localdata/integration/model/output/{DataType}DataResponseDTO.java`

```java
package com.deepinnet.localdata.integration.model.output;

/**
 * Description: {description}数据响应DTO
 * 使用通用基类，指定具体的数据记录类型
 * Date: 2025/9/8
 * Author: qoder
 */
public class {DataType}DataResponseDTO extends BaseDataResponseDTO<{DataType}DataRecord> {

    private static final long serialVersionUID = 1L;

    // 继承所有基类功能，无需额外代码
    // 泛型指定为{DataType}DataRecord，确保result字段的类型安全
}
```

## 3. 客户端接口模板

文件路径: `sz-data-integration/src/main/java/com/deepinnet/localdata/integration/{DataType}DataClient.java`

```java
package com.deepinnet.localdata.integration;

import com.deepinnet.localdata.integration.config.GenerateDefaultClient;
import com.deepinnet.localdata.integration.model.input.BaseQueryDTO;
import com.deepinnet.localdata.integration.model.output.{DataType}DataResponseDTO;

/**
 * Description: {description}数据查询客户端接口
 * Date: 2025/9/8
 * Author: qoder
 */
@GenerateDefaultClient
public interface {DataType}DataClient {

    /**
     * 获取{description}数据
     * 根据分页参数和查询条件获取{description}数据
     *
     * @param queryDTO 查询参数，包含分页参数和搜索条件
     * @return {description}数据响应
     */
    {DataType}DataResponseDTO get{DataType}Data(BaseQueryDTO queryDTO);

    /**
     * 获取{description}数据（简化方法）
     * 使用指定分页参数获取数据
     *
     * @param pageNo   页号，默认为1
     * @param pageSize 页面大小，默认为10，最大100
     * @param search   搜索条件，JSON字符串格式，为空则传""
     * @return {description}数据响应
     */
    {DataType}DataResponseDTO get{DataType}Data(String pageNo, String pageSize, String search);

    /**
     * 获取{description}数据（最简化方法）
     * 使用默认参数获取第一页数据
     *
     * @return {description}数据响应
     */
    {DataType}DataResponseDTO get{DataType}Data();
}
```

## 4. 客户端实现类模板

文件路径: `sz-data-integration/src/main/java/com/deepinnet/localdata/integration/impl/{DataType}DataClientImpl.java`

```java
package com.deepinnet.localdata.integration.impl;

import com.deepinnet.localdata.integration.{DataType}DataClient;
import com.deepinnet.localdata.integration.constants.ActionConstants;
import com.deepinnet.localdata.integration.http.BaseHttpRequestClient;
import com.deepinnet.localdata.integration.http.HttpMethodEnum;
import com.deepinnet.localdata.integration.http.HttpRequestParamNew;
import com.deepinnet.localdata.integration.model.input.BaseQueryDTO;
import com.deepinnet.localdata.integration.model.output.{DataType}DataResponseDTO;
import com.deepinnet.localdata.integration.impl.DaPengTokenClientImpl;
import org.apache.commons.collections4.MultiValuedMap;
import org.apache.commons.collections4.multimap.ArrayListValuedHashMap;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * Description: {description}数据查询客户端实现类
 * Date: 2025/9/8
 * Author: qoder
 */
@Component
public class {DataType}DataClientImpl extends BaseHttpRequestClient implements {DataType}DataClient {

    @Resource
    private DaPengTokenClientImpl daPengTokenClient;

    @Override
    public {DataType}DataResponseDTO get{DataType}Data(BaseQueryDTO queryDTO) {
        System.out.println("开始调用{description}数据接口获取数据，参数: " + queryDTO);

        // 验证并修正查询参数
        if (queryDTO == null) {
            queryDTO = BaseQueryDTO.createDefault();
        }
        queryDTO.validatePagination();

        // 直接在这里替换Token占位符
        String action = ActionConstants.{DATA_TYPE}_DATA_GET_DATA_JSON;
        if (action.contains(ActionConstants.DA_PENG_TOKEN_PLACEHOLDER)) {
            // 获取当前有效的Token
            String token = daPengTokenClient.getCurrentValidToken();
            if (token != null && !token.isEmpty()) {
                // 替换占位符
                action = action.replace(ActionConstants.DA_PENG_TOKEN_PLACEHOLDER, token);
                System.out.println("Token替换完成: " + ActionConstants.{DATA_TYPE}_DATA_GET_DATA_JSON + " -> " + action);
            } else {
                System.out.println("无法获取有效Token，使用原始URL");
            }
        }

        // 构建查询参数
        MultiValuedMap<String, String> paramMap = new ArrayListValuedHashMap<>();
        paramMap.put("pageNo", queryDTO.getPageNo());
        paramMap.put("pageSize", queryDTO.getPageSize());
        if (queryDTO.getSearch() != null && !queryDTO.getSearch().isEmpty()) {
            paramMap.put("search", queryDTO.getSearch());
        }

        // 构建HTTP请求参数
        HttpRequestParamNew param = HttpRequestParamNew.builder()
                .method(HttpMethodEnum.GET)
                .action(action)
                .address("http://************:8280") // 数据服务器地址
                .requestParam(paramMap)
                .build();

        {DataType}DataResponseDTO {dataType}DataResponseDTO = httpRequest(param, {DataType}DataResponseDTO.class, null);
        System.out.println("结束调用{description}数据接口获取数据");
        return {dataType}DataResponseDTO;
    }

    @Override
    public {DataType}DataResponseDTO get{DataType}Data(String pageNo, String pageSize, String search) {
        BaseQueryDTO queryDTO = new BaseQueryDTO(pageNo, pageSize, search);
        return get{DataType}Data(queryDTO);
    }

    @Override
    public {DataType}DataResponseDTO get{DataType}Data() {
        return get{DataType}Data(BaseQueryDTO.createDefault());
    }
}
```

## 5. 控制器模板

文件路径: `sz-test/src/main/java/com/deepinnet/localdata/controller/{DataType}DataController.java`

```java
package com.deepinnet.localdata.controller;

import com.deepinnet.localdata.integration.{DataType}DataClient;
import com.deepinnet.localdata.integration.model.input.BaseQueryDTO;
import com.deepinnet.localdata.integration.model.output.{DataType}DataResponseDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * Description: {description}数据接口测试控制器
 * Date: 2025/9/8
 * Author: qoder
 */
@Api(tags = "{description}数据接口")
@RestController
@RequestMapping("/{requestMapping}")
@Slf4j
public class {DataType}DataController {

    @Resource
    private {DataType}DataClient {dataType}DataClient;

    /**
     * 获取{description}数据（默认参数）
     */
    @ApiOperation(value = "获取{description}数据（默认）", notes = "使用默认参数获取{description}数据JSON数据")
    @GetMapping("/data")
    public {DataType}DataResponseDTO getData() {
        try {
            log.info("开始调用大鹏接口获取{description}数据（默认参数）");

            {DataType}DataResponseDTO response = {dataType}DataClient.get{DataType}Data();

            log.info("成功获取{description}数据，总记录数: {}", response.getTotalRecords());
            return response;

        } catch (Exception e) {
            log.error("获取{description}数据失败", e);
            throw e;
        }
    }

    /**
     * 获取{description}数据（分页查询）
     */
    @ApiOperation(value = "获取{description}数据（分页）", notes = "使用指定分页参数获取{description}数据JSON数据")
    @GetMapping("/data/paged")
    public {DataType}DataResponseDTO getDataPaged(
            @ApiParam(value = "页号，默认为1", example = "1") @RequestParam(defaultValue = "1") String pageNo,
            @ApiParam(value = "页面大小，默认为10，最大100", example = "10") @RequestParam(defaultValue = "10") String pageSize,
            @ApiParam(value = "搜索条件，JSON字符串格式", example = "{}") @RequestParam(defaultValue = "") String search) {
        try {
            log.info("开始调用大鹏接口获取{description}数据（分页查询），页号: {}, 页面大小: {}, 搜索条件: {}", pageNo, pageSize, search);

            {DataType}DataResponseDTO response = {dataType}DataClient.get{DataType}Data(pageNo, pageSize, search);

            log.info("成功获取{description}数据，总记录数: {}, 当前页: {}, 页面大小: {}", 
                    response.getTotalRecords(), response.getPageNo(), response.getPageSize());
            return response;

        } catch (Exception e) {
            log.error("获取{description}数据失败", e);
            throw e;
        }
    }

    /**
     * 获取{description}数据（POST方式）
     */
    @ApiOperation(value = "获取{description}数据（POST）", notes = "使用POST方式提交查询参数获取{description}数据JSON数据")
    @PostMapping("/data")
    public {DataType}DataResponseDTO getDataByPost(@RequestBody BaseQueryDTO queryDTO) {
        try {
            log.info("开始调用大鹏接口获取{description}数据（POST方式），查询参数: {}", queryDTO);

            {DataType}DataResponseDTO response = {dataType}DataClient.get{DataType}Data(queryDTO);

            log.info("成功获取{description}数据，总记录数: {}, 当前页: {}, 页面大小: {}", 
                    response.getTotalRecords(), response.getPageNo(), response.getPageSize());
            return response;

        } catch (Exception e) {
            log.error("获取{description}数据失败", e);
            throw e;
        }
    }

    /**
     * 测试{description}数据接口连通性
     */
    @ApiOperation(value = "测试{description}数据接口", notes = "测试{description}数据接口的连通性和基本功能")
    @GetMapping("/test")
    public String testConnection() {
        try {
            log.info("开始测试{description}数据接口连通性");

            {DataType}DataResponseDTO response = {dataType}DataClient.get{DataType}Data("1", "5", "");

            if (response != null && response.isSuccess()) {
                String message = String.format("{description}数据接口测试成功！总记录数: %d, 返回数据条数: %d", 
                        response.getTotalRecords(), 
                        response.getResultList() != null ? response.getResultList().size() : 0);
                log.info(message);
                return message;
            } else {
                String message = "{description}数据接口测试失败：响应数据异常";
                log.warn(message);
                return message;
            }

        } catch (Exception e) {
            String message = "{description}数据接口测试失败：" + e.getMessage();
            log.error(message, e);
            return message;
        }
    }
}
```

## 6. 常量定义

在 `ActionConstants.java` 中添加：

```java
/**
 * {description}数据获取数据接口
 */
public static final String {DATA_TYPE}_DATA_GET_DATA_JSON = "/services/{apiPath}/"
        + DA_PENG_TOKEN_PLACEHOLDER + "/getDataJson";
```

## 7. Bean工厂注册

在 `GenerateDefaultClientBeanFactoryPostProcess.java` 中添加：

```java
checkAndCreateDefaultProxy(beanFactory, "com.deepinnet.localdata.integration.{DataType}DataClient");
```

## 使用说明

1. 复制对应的模板代码
2. 替换所有模板变量
3. 根据实际API响应调整数据记录类的字段
4. 更新常量定义中的API路径
5. 在Bean工厂中注册新的客户端

## 示例

假设要创建"设备数据"接口：

- `{DataType}` = `Device`
- `{dataType}` = `device`
- `{DATA_TYPE}` = `DEVICE`
- `{description}` = `设备`
- `{apiPath}` = `DEVICE_DB/deviceinfo`
- `{requestMapping}` = `device-data`

按照模板替换变量即可快速生成完整的接口代码。
