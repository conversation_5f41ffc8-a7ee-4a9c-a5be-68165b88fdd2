# 公共设施信息接口实现文档

## 概述
本文档说明了公共设施信息接口的实现，目前包括公厕信息接口、医院信息接口、娱乐场所信息接口、停车场信息接口、古村落信息接口、古村落2信息接口、景区信息接口和旅行社和网点信息接口。

## 已实现的接口

### 1. 公厕信息接口
- **接口地址**: `/services/ZWFWSJGLJ_DB_92/gcsvh/a90c6ea4baf488a5c6d703142263241f_40dc1e82239580472c0471b370601f68/getDataJson`
- **接口常量**: `ActionConstants.PUBLIC_TOILET_GET_DATA_JSON`
- **客户端接口**: `PublicToiletClient`
- **实现类**: `PublicToiletClientImpl`
- **控制器**: `PublicToiletController`
- **请求映射**: `/public-toilet/*`

### 2. 医院信息接口
- **接口地址**: `/services/ZWFWSJGLJ_DB_88/yybgw/a90c6ea4baf488a5c6d703142263241f_40dc1e82239580472c0471b370601f68/getDataJson`
- **接口常量**: `ActionConstants.HOSPITAL_GET_DATA_JSON`
- **客户端接口**: `HospitalClient`
- **实现类**: `HospitalClientImpl`
- **控制器**: `HospitalController`
- **请求映射**: `/hospital/*`

### 3. 娱乐场所信息接口
- **接口地址**: `/services/ZWFWSJGLJ_DB_86/ylcslbz/a90c6ea4baf488a5c6d703142263241f_40dc1e82239580472c0471b370601f68/getDataJson`
- **接口常量**: `ActionConstants.ENTERTAINMENT_VENUE_GET_DATA_JSON`
- **客户端接口**: `EntertainmentVenueClient`
- **实现类**: `EntertainmentVenueClientImpl`
- **控制器**: `EntertainmentVenueController`
- **请求映射**: `/entertainment-venue/*`

### 4. 停车场信息接口
- **接口地址**: `/services/ZWFWSJGLJ_DB_75/tcccej/a90c6ea4baf488a5c6d703142263241f_40dc1e82239580472c0471b370601f68/getDataJson`
- **接口常量**: `ActionConstants.PARKING_LOT_GET_DATA_JSON`
- **客户端接口**: `ParkingLotClient`
- **实现类**: `ParkingLotClientImpl`
- **控制器**: `ParkingLotController`
- **请求映射**: `/parking-lot/*`

### 5. 古村落信息接口
- **接口地址**: `/services/ZWFWSJGLJ_DB_58/gclsdi/a90c6ea4baf488a5c6d703142263241f_40dc1e82239580472c0471b370601f68/getDataJson`
- **接口常量**: `ActionConstants.ANCIENT_VILLAGE_GET_DATA_JSON`
- **客户端接口**: `AncientVillageClient`
- **实现类**: `AncientVillageClientImpl`
- **控制器**: `AncientVillageController`
- **请求映射**: `/ancient-village/*`

### 6. 古村落2信息接口
- **接口地址**: `/services/ZWFWSJGLJ_DB_139/gcl2/a90c6ea4baf488a5c6d703142263241f_40dc1e82239580472c0471b370601f68/getDataJson`
- **接口常量**: `ActionConstants.ANCIENT_VILLAGE2_GET_DATA_JSON`
- **客户端接口**: `AncientVillage2Client`
- **实现类**: `AncientVillage2ClientImpl`
- **控制器**: `AncientVillage2Controller`
- **请求映射**: `/ancient-village2/*`

### 7. 景区信息接口
- **接口地址**: `/services/ZWFWSJGLJ_DB_49/jqwbq/a90c6ea4baf488a5c6d703142263241f_40dc1e82239580472c0471b370601f68/getDataJson`
- **接口常量**: `ActionConstants.SCENIC_AREA_GET_DATA_JSON`
- **客户端接口**: `ScenicAreaClient`
- **实现类**: `ScenicAreaClientImpl`
- **控制器**: `ScenicAreaController`
- **请求映射**: `/scenic-area/*`

### 8. 旅行社和网点信息接口
- **接口地址**: `/services/ZWFWSJGLJ_DB_50/lxshwdame/a90c6ea4baf488a5c6d703142263241f_40dc1e82239580472c0471b370601f68/getDataJson`
- **接口常量**: `ActionConstants.TRAVEL_AGENCY_GET_DATA_JSON`
- **客户端接口**: `TravelAgencyClient`
- **实现类**: `TravelAgencyClientImpl`
- **控制器**: `TravelAgencyController`
- **请求映射**: `/travel-agency/*`

## 接口功能

公厕信息接口、医院信息接口、娱乐场所信息接口、停车场信息接口、古村落信息接口、古村落2信息接口、景区信息接口和旅行社和网点信息接口都提供以下功能：

### 1. 基本查询方法
- `getData()` - 使用默认参数获取第一页数据
- `getDataPaged()` - 使用指定分页参数获取数据
- `getDataByPost()` - 使用POST方式提交查询参数

### 2. 测试方法
- `testConnection()` - 测试接口连通性和基本功能

### 3. 支持的参数
- **pageNo**: 页号，默认为1
- **pageSize**: 页面大小，默认为10，最大100
- **search**: 搜索条件，JSON字符串格式
- **token**: 动态访问令牌（可选）

## 数据结构

### 公厕信息 (PublicToiletResponseDTO)
包含以下主要字段：

**基础信息**：
- 公厕基本信息（ID、名称、编号、类型、等级）
- 位置信息（详细地址、区域、街道、经纬度）
- 开放时间和24小时开放标识

**管理信息**：
- 管理机构、管理员信息、联系电话
- 建设年份、建筑面积
- 服务质量等级和设施设备状况

**设施配置**：
- 厕位配置（男厕位数、女厕位数）
- 无障碍设施、母婴室、第三卫生间
- 基础设施（免费厕纸、洗手液、烘手器、空调、WiFi）
- 安全设施（监控设备）

**服务信息**：
- 清洁频次、维护状态、维护计划
- 停车位数量、周边环境、交通便利程度
- 使用人流量、服务评价、投诉建议

**操作记录**：
- 数据来源、创建时间、更新时间
- 备注信息

### 医院信息 (HospitalResponseDTO)
包含以下主要字段：

**基础信息**：
- 医院基本信息（ID、名称、编号、类型、等级、分类）
- 位置信息（详细地址、区域、街道、经纬度）
- 联系信息（电话、急救电话、传真、邮箱、网站）

**机构信息**：
- 管理人员（法人代表、院长姓名）
- 建院年份、医院规模、占地面积、建筑面积
- 人员配置（职工、医师、护士人数）

**服务信息**：
- 医疗资源（床位数量、设备总值）
- 营业时间、急诊服务、挂号方式
- 医保类型、重点科室、特色服务

**质量信息**：
- 服务质量等级、患者满意度
- 医院荣誉、认证资质
- 管理监督信息和投诉处理机制

### 娱乐场所信息 (EntertainmentVenueResponseDTO)
包含以下主要字段：

**基础信息**：
- 场所基本信息（ID、名称、编号、类型、类别）
- 位置信息（详细地址、区域、街道、经纬度）
- 联系信息（电话、传真、邮箱、网站）

**经营信息**：
- 经营方式、开业年份、经营面积
- 人员配置（员工人数、容纳人数）
- 法人代表、经营者姓名

**许可证信息**：
- 各类许可证号（经营、消防、卫生、文化）
- 许可证有效期和年检情况

**服务信息**：
- 主要经营项目、服务特色、设施设备
- 营业时间、消费标准、价格区间
- 安全措施、应急预案

**管理信息**：
- 运营状态、服务质量等级
- 客户满意度、投诉建议
- 监管部门、信用等级、荣誉奖项

### 停车场信息 (ParkingLotResponseDTO)
包含以下主要字段：

**基础信息**：
- 停车场基本信息（ID、名称、编号、类型、等级、分类）
- 位置信息（详细地址、区域、街道、经纬度）
- 联系信息（电话、传真、邮箱、网站）

**规模信息**：
- 总车位数、小型车位数、大型车位数
- 建设年份、占地面积、建筑面积
- 层数信息（地上层数、地下层数）

**设施信息**：
- 残疾人车位数、充电桩数量
- 智能化设施（管理系统、监控设备、LED显示屏、车牌识别）
- 基础设施（照明、通风、消防、安防）

**经营信息**：
- 经营方式、收费类型、计费单位
- 收费标准（小型车、大型车白天和夜间费率）
- 免费停车时长、包月费用、包年费用
- 营业时间（工作日和周末）
- 管理员信息、投资方

**服务信息**：
- 服务质量等级、服务特色、便民措施
- 周边环境、交通便利程度
- 实时车位状况、预约服务
- 客户满意度、投诉建议

### 古村落信息 (AncientVillageResponseDTO)
包含以下主要字段：

**基础信息**：
- 古村落基本信息（ID、名称、编号、类型、等级、分类）
- 位置信息（省份、城市、区县、街道、详细地址、经纬度）
- 历史信息（建村年代、历史年代）

**规模信息**：
- 村落面积、人口数量、户数
- 主要民族、主要语言
- 古建筑数量

**文化信息**：
- 建筑风格、主要建筑材料
- 文物保护单位级别、文物保护编号
- 历史文化价值、传统文化特色
- 民俗活动、传统手工艺、特色美食、方言特色

**旅游信息**：
- 旅游开发状况、年游客量
- 门票价格、开放时间、最佳游览季节
- 主要景点、交通便利程度

**设施信息**：
- 住宿设施、餐饮设施、购物设施
- 医疗设施、通信设施
- 基础设施完善程度

**环境信息**：
- 环境保护状况、生态环境质量
- 污染治理情况、绿化覆盖率、水资源状况

**管理信息**：
- 保护措施、开发规划
- 管理机构、负责人信息、联系方式
- 荣誉称号、获奖情况、媒体报道
- 知名度、发展前景、存在问题、改进建议

### 古村落2信息 (AncientVillage2ResponseDTO)
包含与古村落信息相同的数据结构，主要字段包括：

**基础信息**：
- 古村落基本信息（ID、名称、编号、类型、等级、分类）
- 位置信息（省份、城市、区县、街道、详细地址、经纬度）
- 历史信息（建村年代、历史年代）

**规模信息**：
- 村落面积、人口数量、户数
- 主要民族、主要语言
- 古建筑数量

**文化信息**：
- 建筑风格、主要建筑材料
- 文物保护单位级别、文物保护编号
- 历史文化价值、传统文化特色
- 民俗活动、传统手工艺、特色美食、方言特色

**旅游信息**：
- 旅游开发状况、年游客量
- 门票价格、开放时间、最佳游览季节
- 主要景点、交通便利程度

**设施信息**：
- 住宿设施、餐饮设施、购物设施
- 医疗设施、通信设施
- 基础设施完善程度

**环境信息**：
- 环境保护状况、生态环境质量
- 污染治理情况、绿化覆盖率、水资源状况

**管理信息**：
- 保护措施、开发规划
- 管理机构、负责人信息、联系方式

### 景区信息 (ScenicAreaResponseDTO)
包含以下主要字段：

**基础信息**：
- 景区基本信息（ID、名称、编号、类型、等级、分类）
- 位置信息（省份、城市、区县、街道、详细地址、经纬度）
- 景区面积、开发年份、建设投资

**资源特色**：
- 主要景观类型、自然资源特色、人文资源特色
- 核心景点、特色景观

**门票信息**：
- 门票价格、淡季门票价格、旺季门票价格
- 优惠政策、开放时间

**旅游信息**：
- 最佳游览季节、建议游览时长
- 年游客量、日最大承载量、旅游收入

**管理信息**：
- 管理机构、法人代表、景区经理
- 联系电话、传真、邮箱、官方网站、微信公众号

**设施信息**：
- 停车场信息（数量、停车位）
- 游客中心、导游服务、讲解服务
- 餐饮设施、住宿设施、购物设施、娱乐设施
- 医疗设施、通信设施、无障碍设施
- 安全设施、环保设施、智慧旅游设施

**交通信息**：
- 交通便利程度、公共交通、自驾路线

**环境信息**：
- 环境质量等级、空气质量、水质状况
- 噪音控制、绿化覆盖率、生态保护措施

**服务质量**：
- 服务质量等级、游客满意度
- 服务投诉率、安全事故率

**荣誉资质**：
- 获得荣誉、认证资质、获奖情况
- 媒体报道、知名度等级、品牌价值

**发展规划**：
- 发展规划、扩建计划、投资规划
- 存在问题、改进措施、发展前景

**监管信息**：
- 监管部门、监管措施、检查频次、合规状态

### 旅行社和网点信息 (TravelAgencyResponseDTO)
包含以下主要字段：

**基础信息**：
- 旅行社基本信息（ID、名称、编号、类型、等级）
- 位置信息（省份、城市、区县、街道、详细地址、经纬度）
- 联系信息（电话、传真、邮箱、网站、微信）

**企业信息**：
- 统一社会信用代码、工商注册号、组织机构代码
- 法人代表、总经理、成立日期
- 注册资本、实收资本、企业性质

**经营许可**：
- 旅行社业务经营许可证编号、颁发日期、有效期
- 许可证颁发机关、经营状态、年检状态
- 质量保证金、保证金缴存银行、账号

**人员配置**：
- 从业人员总数、导游人数、领队人数
- 计调人员、销售人员、财务人员人数

**网点信息**：
- 总门店数量、直营门店数量、加盟门店数量
- 网点分布城市数、主要业务类型

**业务范围**：
- 出境游业务、入境游业务、国内游业务
- 商务旅行业务、会议会展业务、在线旅游平台

**经营数据**：
- 年营业收入、年接待游客量
- 年组织出境游人数、年组织国内游人数
- 年接待入境游客数、主要目的地国家/地区

**合作伙伴**：
- 合作航空公司、合作酒店集团、合作景区

**服务质量**：
- 服务质量等级、客户满意度
- 投诉处理满意度、年投诉量、重大安全事故次数

**荣誉资质**：
- 获得荣誉、行业认证、获奖情况
- 行业排名、品牌价值、信用等级

**合规管理**：
- 诚信记录、违法违规记录、行政处罚记录
- 监管部门、监管措施、检查频次、合规状态

**发展规划**：
- 发展规划、投资计划、扩张计划
- 数字化转型情况、疫情影响评估、恢复情况
- 发展前景、存在问题、改进措施

## API接口示例

### GET请求（默认参数）
```
GET /public-toilet/data
GET /hospital/data
GET /entertainment-venue/data
GET /parking-lot/data
GET /ancient-village/data
GET /ancient-village2/data
GET /scenic-area/data
GET /travel-agency/data
```

### GET请求（带参数）
```
GET /public-toilet/data/paged?pageNo=1&pageSize=20&search={}
GET /hospital/data/paged?pageNo=1&pageSize=20&search={}
GET /entertainment-venue/data/paged?pageNo=1&pageSize=20&search={}
GET /parking-lot/data/paged?pageNo=1&pageSize=20&search={}
GET /ancient-village/data/paged?pageNo=1&pageSize=20&search={}
GET /ancient-village2/data/paged?pageNo=1&pageSize=20&search={}
GET /scenic-area/data/paged?pageNo=1&pageSize=20&search={}
GET /travel-agency/data/paged?pageNo=1&pageSize=20&search={}
```

### POST请求
```
POST /public-toilet/data
POST /hospital/data
POST /entertainment-venue/data
POST /parking-lot/data
POST /ancient-village/data
POST /ancient-village2/data
POST /scenic-area/data
POST /travel-agency/data

Content-Type: application/json
{
    "pageNo": "1",
    "pageSize": "10",
    "search": "",
    "token": ""
}
```

### 测试接口
```
GET /public-toilet/test
GET /hospital/test
GET /entertainment-venue/test
GET /parking-lot/test
GET /ancient-village/test
GET /ancient-village2/test
GET /scenic-area/test
GET /travel-agency/test
```

## 配置说明

1. 公厕信息接口、医院信息接口、娱乐场所信息接口、停车场信息接口、古村落信息接口、古村落2信息接口、景区信息接口和旅行社和网点信息接口都使用了`@GenerateDefaultClient`注解，支持自动代理生成
2. 实现类使用`@Service`注解，自动注册为Spring Bean
3. 已在`GenerateDefaultClientBeanFactoryPostProcess`中配置了接口扫描

## 使用方法

1. 启动应用程序（`ApplicationStart`）
2. 访问Swagger文档查看接口详情
3. 使用HTTP客户端或浏览器测试接口
4. 在其他服务中注入`PublicToiletClient`、`HospitalClient`、`EntertainmentVenueClient`、`ParkingLotClient`、`AncientVillageClient`、`AncientVillage2Client`、`ScenicAreaClient`或`TravelAgencyClient`接口即可调用

## 注意事项

1. 接口地址中的token参数与URL路径中的服务标识符不同
2. 分页查询支持最大100条记录
3. 搜索条件需要传递JSON格式字符串
4. 所有接口都支持异常处理和日志记录
5. 公厕信息接口、医院信息接口、娱乐场所信息接口、停车场信息接口、古村落信息接口、古村落2信息接口、景区信息接口和旅行社和网点信息接口都遵循统一的设计模式和代码结构