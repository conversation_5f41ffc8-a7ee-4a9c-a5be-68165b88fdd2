<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.deepinnet</groupId>
    <artifactId>sz-data-integration</artifactId>
    <version>1.1.20250904-SNAPSHOT</version>
    <packaging>jar</packaging>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <digital-boot-starter.version>1.0.0.20241015-RELEASE</digital-boot-starter.version>
        <digitaltwincommon.version>1.3-SNAPSHOT</digitaltwincommon.version>
        <mapstruct.version>1.5.0.Final</mapstruct.version>
        <lombok.version>1.18.34</lombok.version>
        <common-lang3.version>3.12.0</common-lang3.version>
        <commons-collections4.version>4.4</commons-collections4.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.deepinnet</groupId>
            <artifactId>digital-boot-starter</artifactId>
            <version>${digital-boot-starter.version}</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.deepinnet</groupId>
            <artifactId>digitaltwincommon</artifactId>
            <version>${digitaltwincommon.version}</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>${mapstruct.version}</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <version>${mapstruct.version}</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.hikvision.ga</groupId>
            <artifactId>artemis-http-client</artifactId>
            <version>1.1.13.RELEASE</version>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.13</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpcore</artifactId>
            <version>4.4.15</version>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.8.25</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>${common-lang3.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
            <version>${commons-collections4.version}</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
            <scope>provided</scope>
        </dependency>
    </dependencies>

    <build>
        <finalName>sz-data-integration</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>11</source>
                    <target>11</target>
                    <encoding>UTF-8</encoding>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <!-- 要将源码放上去，需要加入这个插件 -->
            <plugin>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.0.1</version>
                <configuration>
                    <attach>true</attach>
                </configuration>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>

        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.xml</include>
                    <include>**/*.yml</include>
                    <include>*.json</include>
                    <include>**/META-INF/**</include>
                    <include>static/**</include>
                    <include>**/*.txt</include>
                </includes>
                <filtering>true</filtering>
            </resource>
        </resources>
    </build>

    <distributionManagement>
        <repository>
            <id>rdc-releases</id>
            <name>aliyun mvn release Repository</name>
            <url>https://packages.aliyun.com/maven/repository/2283499-release-fXoFPE/</url>
        </repository>
        <snapshotRepository>
            <id>rdc-snapshots</id>
            <name>aliyun mvn snapshot Repository</name>
            <url>https://packages.aliyun.com/maven/repository/2283499-snapshot-U7MepR/</url>
        </snapshotRepository>
    </distributionManagement>
</project>