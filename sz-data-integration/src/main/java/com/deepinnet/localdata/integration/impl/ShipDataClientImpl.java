package com.deepinnet.localdata.integration.impl;

import com.deepinnet.localdata.integration.ShipDataClient;
import com.deepinnet.localdata.integration.constants.ActionConstants;
import com.deepinnet.localdata.integration.http.*;
import com.deepinnet.localdata.integration.model.input.BaseQueryDTO;
import com.deepinnet.localdata.integration.model.output.ShipDataResponseDTO;
import org.apache.commons.collections4.MultiValuedMap;
import org.apache.commons.collections4.multimap.ArrayListValuedHashMap;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * Description: 船舶数据查询客户端实现类
 * 使用通用响应结构的示例
 * Date: 2025/9/8
 * Author: qoder
 */
@Component
public class ShipDataClientImpl extends BaseHttpRequestClient implements ShipDataClient {

    @Resource
    private DaPengTokenClientImpl daPengTokenClient;

    @Override
    public ShipDataResponseDTO getShipData(BaseQueryDTO queryDTO) {
        System.out.println("开始调用船舶数据接口获取数据，参数: " + queryDTO);

        // 验证并修正查询参数
        if (queryDTO == null) {
            queryDTO = BaseQueryDTO.createDefault();
        }
        queryDTO.validatePagination();

        // 直接在这里替换Token占位符
        String action = ActionConstants.SHIP_DATA_GET_DATA_JSON;
        if (action.contains(ActionConstants.DA_PENG_TOKEN_PLACEHOLDER)) {
            // 获取当前有效的Token
            String token = daPengTokenClient.getCurrentValidToken();
            if (token != null && !token.isEmpty()) {
                // 替换占位符
                action = action.replace(ActionConstants.DA_PENG_TOKEN_PLACEHOLDER, token);
                System.out.println("Token替换完成: " + ActionConstants.SHIP_DATA_GET_DATA_JSON + " -> " + action);
            } else {
                System.out.println("无法获取有效Token，使用原始URL");
            }
        }

        // 构建查询参数
        MultiValuedMap<String, String> paramMap = new ArrayListValuedHashMap<>();
        paramMap.put("pageNo", queryDTO.getPageNo());
        paramMap.put("pageSize", queryDTO.getPageSize());
        if (queryDTO.getSearch() != null && !queryDTO.getSearch().isEmpty()) {
            paramMap.put("search", queryDTO.getSearch());
        }

        // 构建HTTP请求参数
        HttpRequestParamNew param = HttpRequestParamNew.builder()
                .method(HttpMethodEnum.GET)
                .action(action)
                .address("http://************:8280") // 数据服务器地址
                .requestParam(paramMap)
                .build();

        ShipDataResponseDTO shipDataResponseDTO = httpRequestForDaPeng(param, ShipDataResponseDTO.class);
        System.out.println("结束调用船舶数据接口获取数据");
        return shipDataResponseDTO;
    }

    @Override
    public ShipDataResponseDTO getShipData(String pageNo, String pageSize, String search) {
        BaseQueryDTO queryDTO = new BaseQueryDTO(pageNo, pageSize, search);
        return getShipData(queryDTO);
    }

    @Override
    public ShipDataResponseDTO getShipData() {
        return getShipData(BaseQueryDTO.createDefault());
    }
}
