package com.deepinnet.localdata.integration.model.input;

import lombok.Data;

import java.io.Serializable;

/**
 * Description: 通用查询参数DTO
 * 适用于大鹏接口的标准查询参数
 * Date: 2025/9/8
 * Author: qoder
 */
@Data
public class BaseQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 页号，默认为1
     */
    private String pageNo = "1";

    /**
     * 页面大小，默认为10，最大100
     */
    private String pageSize = "10";

    /**
     * 搜索条件，JSON字符串格式
     * 为空则传空字符串""
     */
    private String search = "";

    /**
     * 动态访问令牌（可选）
     * 通常由系统自动管理，不需要手动设置
     */
    private String token;

    /**
     * 构造函数 - 默认参数
     */
    public BaseQueryDTO() {
    }

    /**
     * 构造函数 - 指定分页参数
     *
     * @param pageNo   页号
     * @param pageSize 页面大小
     */
    public BaseQueryDTO(String pageNo, String pageSize) {
        this.pageNo = pageNo != null ? pageNo : "1";
        this.pageSize = pageSize != null ? pageSize : "10";
    }

    /**
     * 构造函数 - 指定所有参数
     *
     * @param pageNo   页号
     * @param pageSize 页面大小
     * @param search   搜索条件
     */
    public BaseQueryDTO(String pageNo, String pageSize, String search) {
        this.pageNo = pageNo != null ? pageNo : "1";
        this.pageSize = pageSize != null ? pageSize : "10";
        this.search = search != null ? search : "";
    }

    /**
     * 验证分页参数
     */
    public void validatePagination() {
        // 验证页号
        try {
            int page = Integer.parseInt(this.pageNo);
            if (page < 1) {
                this.pageNo = "1";
            }
        } catch (NumberFormatException e) {
            this.pageNo = "1";
        }

        // 验证页面大小
        try {
            int size = Integer.parseInt(this.pageSize);
            if (size < 1) {
                this.pageSize = "10";
            } else if (size > 100) {
                this.pageSize = "100";
            }
        } catch (NumberFormatException e) {
            this.pageSize = "10";
        }

        // 确保search不为null
        if (this.search == null) {
            this.search = "";
        }
    }

    /**
     * 创建默认查询参数
     */
    public static BaseQueryDTO createDefault() {
        return new BaseQueryDTO();
    }

    /**
     * 创建指定分页的查询参数
     */
    public static BaseQueryDTO createWithPagination(String pageNo, String pageSize) {
        return new BaseQueryDTO(pageNo, pageSize);
    }

    /**
     * 创建完整的查询参数
     */
    public static BaseQueryDTO createWithSearch(String pageNo, String pageSize, String search) {
        return new BaseQueryDTO(pageNo, pageSize, search);
    }
}
