package com.deepinnet.localdata.integration.http;

import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.util.JsonUtil;
import com.deepinnet.localdata.integration.error.BizErrorCode;
import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.commons.collections4.MultiValuedMap;
import org.apache.commons.collections4.multimap.ArrayListValuedHashMap;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * Description: HttpRequestParam 工具类
 * Date: 2025/3/10
 * Author: lijunheng
 */
public class HttpRequestParamUtils {

    /**
     * 获取请求体的 JSON 字符串
     */
    public static String getRequestBodyJson(Object requestBody) {
        if (requestBody == null) {
            return null;
        }

        validateRequestBody(requestBody);

        if (requestBody instanceof String) {
            return (String) requestBody;
        } else {
            return JsonUtil.toJsonStr(requestBody);
        }
    }

    /**
     * 验证 requestBody
     * 如果是 String，需要判断是否是 Json 格式，如果不是就报错
     * 如果是 8 个基本类型或者枚举就报错
     */
    private static void validateRequestBody(Object requestBody) {
        if (requestBody == null) {
            return;
        }
        
        // 检查是否是基本类型或枚举
        if (isPrimitiveOrEnum(requestBody)) {
            throw new BizException(BizErrorCode.DATA_INVALID.getCode(), 
                "requestBody不能是基本类型或枚举类型: " + requestBody.getClass().getSimpleName());
        }
        
        // 如果是 String，需要验证是否是 Json 格式
        if (requestBody instanceof String) {
            String jsonStr = (String) requestBody;
            if (StringUtils.isNotBlank(jsonStr)) {
                if (!JsonUtil.isValidJson(jsonStr)) {
                    throw new BizException(BizErrorCode.DATA_INVALID.getCode(), 
                        "requestBody是String类型时必须是有效的JSON格式");
                }
            }
        }
    }

    /**
     * 检查是否是基本类型或枚举
     */
    private static boolean isPrimitiveOrEnum(Object obj) {
        Class<?> clazz = obj.getClass();
        return clazz.isPrimitive() || 
               clazz.isEnum() ||
               clazz == Boolean.class ||
               clazz == Byte.class ||
               clazz == Character.class ||
               clazz == Short.class ||
               clazz == Integer.class ||
               clazz == Long.class ||
               clazz == Float.class ||
               clazz == Double.class;
    }

    /**
     * 将 MultiValuedMap 转换为 JSON 字符串
     */
    public static String convertMultiValuedMapToJson(MultiValuedMap<String, String> multiValuedMap) {
        if (multiValuedMap == null || multiValuedMap.isEmpty()) {
            return null;
        }
        
        Map<String, Object> resultMap = new HashMap<>();
        for (String key : multiValuedMap.keySet()) {
            if (multiValuedMap.get(key).size() == 1) {
                resultMap.put(key, multiValuedMap.get(key).iterator().next());
            } else {
                resultMap.put(key, multiValuedMap.get(key));
            }
        }
        
        return JsonUtil.toJsonStr(resultMap);
    }

    public static MultiValuedMap<String, String> convertMapToMultiValuedMap(Map<String, String> map) {
        MultiValuedMap<String, String> multiValuedMap = new ArrayListValuedHashMap<>();
        
        if (map == null || map.isEmpty()) {
            return multiValuedMap;
        }
        
        for (Map.Entry<String, String> entry : map.entrySet()) {
            multiValuedMap.put(entry.getKey(), entry.getValue());
        }
        
        return multiValuedMap;
    }

    public static Map<String, String> convertMultiValuedMapToMap(MultiValuedMap<String, String> multiValuedMap) {
        Map<String, String> map = new HashMap<>();
        
        if (multiValuedMap == null || multiValuedMap.isEmpty()) {
            return map;
        }
        
        for (String key : multiValuedMap.keySet()) {
            // 如果一个键有多个值，我们取第一个值
            // 注意：这种转换可能会丢失一些信息，因为MultiValuedMap可以有多个值
            String firstValue = multiValuedMap.get(key).iterator().next();
            map.put(key, firstValue);
        }
        
        return map;
    }

    /**
     * 将 JSON 字符串转换为 MultiValuedMap
     */
    public static MultiValuedMap<String, String> convertJsonToMultiValuedMap(String jsonStr) {
        MultiValuedMap<String, String> multiValuedMap = new ArrayListValuedHashMap<>();
        
        if (StringUtils.isBlank(jsonStr)) {
            return multiValuedMap;
        }
        
        try {
            Map<String, Object> map = JsonUtil.parseJson(jsonStr, new TypeReference<>() {
            });
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();
                
                if (value instanceof String) {
                    multiValuedMap.put(key, (String) value);
                } else if (value instanceof Iterable) {
                    for (Object item : (Iterable<?>) value) {
                        multiValuedMap.put(key, String.valueOf(item));
                    }
                } else {
                    multiValuedMap.put(key, String.valueOf(value));
                }
            }
        } catch (Exception e) {
            throw new BizException(BizErrorCode.DATA_INVALID.getCode(), "JSON格式错误: " + e.getMessage());
        }
        
        return multiValuedMap;
    }

    public static HttpRequestParamNew convertHttpRequestParam(HttpRequestParam httpRequestParam) {
        return new HttpRequestParamNew.HttpRequestParamNewBuilder()
                .address(httpRequestParam.getAddress())
                .action(httpRequestParam.getAction())
                .method(httpRequestParam.getMethod())
                .requestHeader(convertJsonToMultiValuedMap(httpRequestParam.getRequestHeader()))
                .requestParam(convertJsonToMultiValuedMap(httpRequestParam.getRequestParam()))
                .requestBody(httpRequestParam.getRequestBody())
                .build();
    }
}