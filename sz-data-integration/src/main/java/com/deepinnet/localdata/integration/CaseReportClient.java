package com.deepinnet.localdata.integration;

import com.deepinnet.localdata.integration.model.input.CaseReportRequestDTO;
import com.deepinnet.localdata.integration.model.output.CaseReportResponseDTO;
import com.deepinnet.localdata.integration.model.output.CaseQueryResponseDTO;
import com.deepinnet.localdata.integration.model.output.CaseFlowResponseDTO;

/**
 * 工单上报客户端接口
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
public interface CaseReportClient {

    /**
     * 工单上报
     * 
     * @param request 工单上报请求参数
     * @return 工单上报响应结果
     */
    CaseReportResponseDTO reportCase(CaseReportRequestDTO request);

    /**
     * 工单查询
     * 
     * @param casecode 工单编码
     * @return 工单查询响应结果
     */
    CaseQueryResponseDTO queryCase(String casecode);

    /**
     * 工单流程查询
     * 
     * @param casecode 工单编码
     * @return 工单流程查询响应结果
     */
    CaseFlowResponseDTO queryCaseFlow(String casecode);
}