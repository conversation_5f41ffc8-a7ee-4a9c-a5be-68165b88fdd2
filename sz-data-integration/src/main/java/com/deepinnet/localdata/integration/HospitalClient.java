package com.deepinnet.localdata.integration;

import com.deepinnet.localdata.integration.config.GenerateDefaultClient;
import com.deepinnet.localdata.integration.model.input.HospitalQueryDTO;
import com.deepinnet.localdata.integration.model.output.HospitalResponseDTO;

/**
 * Description: 医院信息查询客户端接口
 * Date: 2025/8/27
 * Author: qoder
 */
@GenerateDefaultClient
public interface HospitalClient {

    /**
     * 获取医院信息数据
     * 根据分页参数和查询条件获取医院信息数据
     *
     * @param queryDTO 查询参数，包含分页参数和搜索条件
     * @return 医院信息数据响应
     */
    HospitalResponseDTO getHospitalData(HospitalQueryDTO queryDTO);

    /**
     * 获取医院信息数据（简化方法）
     * 使用指定分页参数获取数据
     *
     * @param pageNo   页号，默认为1
     * @param pageSize 页面大小，默认为10，最大100
     * @param search   搜索条件，JSON字符串格式，为空则传""
     * @return 医院信息数据响应
     */
    HospitalResponseDTO getHospitalData(String pageNo, String pageSize, String search);

    /**
     * 获取医院信息数据（最简化方法）
     * 使用默认参数获取第一页数据
     *
     * @return 医院信息数据响应
     */
    HospitalResponseDTO getHospitalData();
}