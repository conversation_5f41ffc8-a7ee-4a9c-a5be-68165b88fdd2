package com.deepinnet.localdata.integration.error;

/**
 * Description: 重复执行异常
 * Date: 2025/6/5
 * Author: lijunheng
 */
public class RepeatExecException extends RuntimeException {
    public RepeatExecException() {
    }

    public RepeatExecException(String message) {
        super(message);
    }

    public RepeatExecException(String message, Throwable cause) {
        super(message, cause);
    }

    public RepeatExecException(Throwable cause) {
        super(cause);
    }

    public RepeatExecException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
