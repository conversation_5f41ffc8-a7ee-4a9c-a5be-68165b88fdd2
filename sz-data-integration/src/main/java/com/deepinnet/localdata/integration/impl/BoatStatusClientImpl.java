package com.deepinnet.localdata.integration.impl;

import com.deepinnet.localdata.integration.BoatStatusClient;
import com.deepinnet.localdata.integration.constants.ActionConstants;
import com.deepinnet.localdata.integration.http.BaseHttpRequestClient;
import com.deepinnet.localdata.integration.http.HttpMethodEnum;
import com.deepinnet.localdata.integration.http.HttpRequestParamNew;
import com.deepinnet.localdata.integration.model.input.BoatStatusQueryDTO;
import com.deepinnet.localdata.integration.model.output.BoatStatusResponseDTO;
import com.deepinnet.localdata.integration.impl.DaPengTokenClientImpl;
import org.apache.commons.collections4.MultiValuedMap;
import org.apache.commons.collections4.multimap.ArrayListValuedHashMap;
import org.springframework.stereotype.Component;

/**
 * 船舶状态信息Client实现
 * Date: 2024/8/29
 * Author: lijunheng
 */
@Component
public class BoatStatusClientImpl extends BaseHttpRequestClient implements BoatStatusClient {

    private DaPengTokenClientImpl daPengTokenClient;

    public BoatStatusClientImpl(DaPengTokenClientImpl daPengTokenClient) {
        this.daPengTokenClient = daPengTokenClient;
    }

    @Override
    public BoatStatusResponseDTO getBoatStatusData(BoatStatusQueryDTO queryDTO) {
        System.out.println("开始调用大鹏接口获取船舶状态信息数据，参数: " + queryDTO);

        // 直接在这里替换Token占位符
        String action = ActionConstants.BOAT_STATUS_GET_DATA_JSON;
        if (action.contains(ActionConstants.DA_PENG_TOKEN_PLACEHOLDER)) {
            // 获取当前有效的Token
            String token = daPengTokenClient.getCurrentValidToken();
            if (token != null && !token.isEmpty()) {
                // 替换占位符
                action = action.replace(ActionConstants.DA_PENG_TOKEN_PLACEHOLDER, token);
                System.out.println("Token替换完成: " + ActionConstants.BOAT_STATUS_GET_DATA_JSON + " -> " + action);
            } else {
                System.out.println("无法获取有效Token，使用原始URL");
            }
        }

        // 构建查询参数
        MultiValuedMap<String, String> paramMap = new ArrayListValuedHashMap<>();
        paramMap.put("pageNo", queryDTO.getPageNo() != null ? queryDTO.getPageNo() : "1");
        paramMap.put("pageSize",
                queryDTO.getPageSize() != null ? queryDTO.getPageSize() : "10");
        if (queryDTO.getSearch() != null) {
            paramMap.put("search", queryDTO.getSearch());
        }

        // 构建HTTP请求参数
        HttpRequestParamNew param = HttpRequestParamNew.builder()
                .method(HttpMethodEnum.GET)
                .action(action)
                .address("http://************:8280") // 数据服务器地址
                .requestParam(paramMap)
                .build();

        // 添加重试机制
        int maxRetries = 3;
        int retryDelay = 2000; // 2秒
        BoatStatusResponseDTO boatStatusResponseDTO = null;
        Exception lastException = null;

        for (int i = 0; i <= maxRetries; i++) {
            try {
                boatStatusResponseDTO = httpRequest(param, BoatStatusResponseDTO.class, null);
                System.out.println("结束调用大鹏接口获取船舶状态信息数据");
                return boatStatusResponseDTO;
            } catch (Exception e) {
                lastException = e;
                System.out.println("调用大鹏接口获取船舶状态信息数据失败，第" + (i + 1) + "次尝试");
                if (i < maxRetries) {
                    try {
                        Thread.sleep(retryDelay);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("线程中断", ie);
                    }
                }
            }
        }

        // 如果所有重试都失败了，抛出最后一次异常
        throw new RuntimeException("调用大鹏接口获取船舶状态信息数据失败，已重试" + maxRetries + "次", lastException);
    }

    @Override
    public BoatStatusResponseDTO getBoatStatusData(String pageNo, String pageSize, String search) {
        BoatStatusQueryDTO queryDTO = new BoatStatusQueryDTO();
        queryDTO.setPageNo(pageNo);
        queryDTO.setPageSize(pageSize);
        queryDTO.setSearch(search);
        return getBoatStatusData(queryDTO);
    }

    @Override
    public BoatStatusResponseDTO getBoatStatusData() {
        return getBoatStatusData("1", "10", "");
    }
}