package com.deepinnet.localdata.integration.model.output;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 文件上传响应DTO
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
public class FileUploadResponseDTO {

    /**
     * 自定义响应数据
     */
    @JsonProperty("custom")
    private CustomData custom;

    /**
     * 状态信息
     */
    @JsonProperty("status")
    private StatusInfo status;

    @Data
    public static class CustomData {
        /**
         * 附件GUID
         */
        @JsonProperty("attachguid")
        private String attachguid;
    }

    @Data
    public static class StatusInfo {
        /**
         * 状态码
         */
        @JsonProperty("code")
        private String code;

        /**
         * 状态描述
         */
        @JsonProperty("text")
        private String text;
    }

    /**
     * 创建成功响应
     */
    public static FileUploadResponseDTO success(String attachguid) {
        FileUploadResponseDTO response = new FileUploadResponseDTO();

        CustomData custom = new CustomData();
        custom.setAttachguid(attachguid);
        response.setCustom(custom);

        StatusInfo status = new StatusInfo();
        status.setCode("1");
        status.setText("请求成功");
        response.setStatus(status);

        return response;
    }

    /**
     * 创建失败响应
     */
    public static FileUploadResponseDTO error(String errorMessage) {
        FileUploadResponseDTO response = new FileUploadResponseDTO();

        CustomData custom = new CustomData();
        custom.setAttachguid("");
        response.setCustom(custom);

        StatusInfo status = new StatusInfo();
        status.setCode("0");
        status.setText(errorMessage);
        response.setStatus(status);

        return response;
    }
}