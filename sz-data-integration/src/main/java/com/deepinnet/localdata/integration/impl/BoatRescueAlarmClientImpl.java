package com.deepinnet.localdata.integration.impl;

import com.deepinnet.localdata.integration.BoatRescueAlarmClient;
import com.deepinnet.localdata.integration.constants.ActionConstants;
import com.deepinnet.localdata.integration.http.BaseHttpRequestClient;
import com.deepinnet.localdata.integration.http.HttpMethodEnum;
import com.deepinnet.localdata.integration.http.HttpRequestParamNew;
import com.deepinnet.localdata.integration.model.input.BoatRescueAlarmQueryDTO;
import com.deepinnet.localdata.integration.model.output.BoatRescueAlarmResponseDTO;
import com.deepinnet.localdata.integration.impl.DaPengTokenClientImpl;
import org.apache.commons.collections4.MultiValuedMap;
import org.apache.commons.collections4.multimap.ArrayListValuedHashMap;
import org.springframework.stereotype.Component;

/**
 * 船舶求助报警信息Client实现
 * Date: 2024/8/29
 * Author: qoder
 */
@Component
public class BoatRescueAlarmClientImpl extends BaseHttpRequestClient implements BoatRescueAlarmClient {

    private DaPengTokenClientImpl daPengTokenClient;

    public BoatRescueAlarmClientImpl(DaPengTokenClientImpl daPengTokenClient) {
        this.daPengTokenClient = daPengTokenClient;
    }

    @Override
    public BoatRescueAlarmResponseDTO getBoatRescueAlarmData(BoatRescueAlarmQueryDTO queryDTO) {
        System.out.println("开始调用大鹏接口获取船舶求助报警信息数据，参数: " + queryDTO);

        // 直接在这里替换Token占位符
        String action = ActionConstants.BOAT_RESCUE_ALARM_GET_DATA_JSON;
        if (action.contains(ActionConstants.DA_PENG_TOKEN_PLACEHOLDER)) {
            // 获取当前有效的Token
            String token = daPengTokenClient.getCurrentValidToken();
            if (token != null && !token.isEmpty()) {
                // 替换占位符
                action = action.replace(ActionConstants.DA_PENG_TOKEN_PLACEHOLDER, token);
                System.out.println("Token替换完成: " + ActionConstants.BOAT_RESCUE_ALARM_GET_DATA_JSON + " -> " + action);
            } else {
                System.out.println("无法获取有效Token，使用原始URL");
            }
        }

        // 构建查询参数
        MultiValuedMap<String, String> paramMap = new ArrayListValuedHashMap<>();
        paramMap.put("pageNo", queryDTO.getPageNo() != null ? queryDTO.getPageNo() : "1");
        paramMap.put("pageSize",
                queryDTO.getPageSize() != null ? queryDTO.getPageSize() : "10");
        if (queryDTO.getSearch() != null) {
            paramMap.put("search", queryDTO.getSearch());
        }

        // 构建HTTP请求参数
        HttpRequestParamNew param = HttpRequestParamNew.builder()
                .method(HttpMethodEnum.GET)
                .action(action)
                .address("http://************:8280") // 数据服务器地址
                .requestParam(paramMap)
                .build();

        BoatRescueAlarmResponseDTO boatRescueAlarmResponseDTO = httpRequest(param, BoatRescueAlarmResponseDTO.class,
                null);
        System.out.println("结束调用大鹏接口获取船舶求助报警信息数据");
        return boatRescueAlarmResponseDTO;
    }

    @Override
    public BoatRescueAlarmResponseDTO getBoatRescueAlarmData(String pageNo, String pageSize, String search) {
        BoatRescueAlarmQueryDTO queryDTO = new BoatRescueAlarmQueryDTO();
        queryDTO.setPageNo(pageNo);
        queryDTO.setPageSize(pageSize);
        queryDTO.setSearch(search);
        return getBoatRescueAlarmData(queryDTO);
    }

    @Override
    public BoatRescueAlarmResponseDTO getBoatRescueAlarmData() {
        return getBoatRescueAlarmData("1", "10", "");
    }
}