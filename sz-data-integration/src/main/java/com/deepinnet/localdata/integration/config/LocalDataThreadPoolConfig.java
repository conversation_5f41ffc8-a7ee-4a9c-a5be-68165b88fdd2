package com.deepinnet.localdata.integration.config;

import cn.hutool.core.thread.ThreadFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.ThreadPoolExecutor;

/**

 * 线程池配置类
 *
 * <AUTHOR>
 * @since 2024-08-09 星期五
 **/
@Configuration
public class LocalDataThreadPoolConfig {

    @Bean(name = "localDataScheduleTaskExecutor")
    public ScheduledThreadPoolExecutor localDataScheduleTaskExecutor() {
        return new ScheduledThreadPoolExecutor(Runtime.getRuntime().availableProcessors() * 2, ThreadFactoryBuilder.create().setNamePrefix("local-data-schedule-task-exec-").build(), new ThreadPoolExecutor.CallerRunsPolicy());
    }
}
