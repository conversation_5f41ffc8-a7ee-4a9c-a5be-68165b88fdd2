package com.deepinnet.localdata.integration.service;

import cn.hutool.core.util.StrUtil;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.util.JsonUtil;
import com.deepinnet.localdata.integration.error.BizErrorCode;
import com.deepinnet.localdata.integration.http.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ContentType;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 大鹏Token专用HTTP请求服务
 * 专门用于Token获取，使用独立的Token服务器地址
 * Date: 2025/9/1
 * Author: qoder
 */
@Component
@Slf4j
public class DaPengTokenHttpRequestService extends BaseHttpRequestClient {

    @Resource
    private DaPengApiConnectionInfo connectInfo;

    /**
     * 执行Token获取HTTP请求
     *
     * @param httpRequestParam HTTP请求参数
     * @param type             返回类型
     * @param <T>              泛型类型
     * @return 响应结果
     */
    public <T> T httpRequest(HttpRequestParam httpRequestParam, Object type) {
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("Content-type", ContentType.APPLICATION_JSON.getMimeType());
        setHeader(httpRequestParam, headerMap);

        return super.httpRequest(httpRequestParam, type, ContentType.APPLICATION_JSON);
    }

    /**
     * 设置Token请求头信息
     *
     * @param httpRequestParam HTTP请求参数
     * @param headerMap        请求头Map
     */
    private void setHeader(HttpRequestParam httpRequestParam, Map<String, String> headerMap) {
        // 设置Accept头
        headerMap.put("Accept", ContentType.APPLICATION_JSON.getMimeType());

        httpRequestParam.setRequestHeader(JsonUtil.toJsonStr(headerMap));

        // 设置Token服务器地址
        String tokenServerUrl = connectInfo.getFullTokenServerUrl();
        if (StringUtils.isBlank(tokenServerUrl)) {
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "大鹏Token服务器地址未配置");
        }

        httpRequestParam.setAddress(tokenServerUrl);
    }

    @Override
    public <T> T httpRequest(HttpRequestParam httpRequestParam, Object type, ContentType contentType) {
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("Content-type", contentType.getMimeType());
        setHeader(httpRequestParam, headerMap);

        String requestParam = httpRequestParam.getRequestParam();
        String url = httpRequestParam.getAddress();
        if (StrUtil.isNotBlank(httpRequestParam.getAction())) {
            url = httpRequestParam.getAddress() + httpRequestParam.getAction();
        }

        log.info("发送大鹏Token请求: {}", url);

        HttpResult response = HttpUtils.doGet(url,
                HttpRequestParamUtils.convertJsonToMultiValuedMap(requestParam),
                HttpRequestParamUtils.convertMapToMultiValuedMap(headerMap));

        try {
            log.info("大鹏Token接口响应: {}", response.getBody());
            return super.convertResponse(response.getBody(), type);
        } catch (Exception e) {
            log.error("大鹏Token接口响应解析失败, requestUrl:{}, expected return type:{}, response:{}", url, type.getClass(),
                    response,
                    e);
            throw new BizException(BizErrorCode.DATA_INVALID.getCode(), "大鹏Token接口响应解析失败: " + e.getMessage(), e);
        }
    }
}