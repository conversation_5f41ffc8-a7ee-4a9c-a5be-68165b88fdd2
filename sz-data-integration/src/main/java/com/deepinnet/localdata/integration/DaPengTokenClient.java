package com.deepinnet.localdata.integration;

import com.deepinnet.localdata.integration.config.GenerateDefaultClient;
import com.deepinnet.localdata.integration.model.input.OAuth2TokenRequestDTO;
import com.deepinnet.localdata.integration.model.output.OAuth2TokenResponseDTO;

/**
 * 大鹏Token获取客户端接口
 * Date: 2025/9/1
 * Author: qoder
 */
@GenerateDefaultClient
public interface DaPengTokenClient {

    /**
     * 获取大鹏访问令牌
     *
     * @param request Token请求参数
     * @return Token响应结果
     */
    OAuth2TokenResponseDTO getDaPengToken(OAuth2TokenRequestDTO request);

    /**
     * 获取大鹏访问令牌（使用默认配置）
     *
     * @return Token响应结果
     */
    OAuth2TokenResponseDTO getDaPengToken();

    /**
     * 刷新大鹏访问令牌
     *
     * @param refreshToken 刷新令牌
     * @return 新的Token响应结果
     */
    OAuth2TokenResponseDTO refreshDaPengToken(String refreshToken);
}