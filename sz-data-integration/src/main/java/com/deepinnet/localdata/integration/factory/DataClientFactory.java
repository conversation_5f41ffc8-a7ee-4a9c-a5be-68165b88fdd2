package com.deepinnet.localdata.integration.factory;

import com.deepinnet.localdata.integration.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * Description: 数据客户端工厂类
 * 用于统一管理和获取各种数据客户端
 * Date: 2025/9/8
 * Author: qoder
 */
@Component
@Slf4j
public class DataClientFactory {

    @Resource
    private PublicToiletClient publicToiletClient;

    @Resource
    private HospitalClient hospitalClient;

    @Resource
    private EntertainmentVenueClient entertainmentVenueClient;

    @Resource
    private ParkingLotClient parkingLotClient;

    @Resource
    private AncientVillageClient ancientVillageClient;

    @Resource
    private AncientVillage2Client ancientVillage2Client;

    @Resource
    private ScenicAreaClient scenicAreaClient;

    @Resource
    private TravelAgencyClient travelAgencyClient;

    @Resource
    private ShipDataClient shipDataClient;

    @Resource
    private BoatEquipmentClient boatEquipmentClient;

    /**
     * 客户端类型枚举
     */
    public enum ClientType {
        PUBLIC_TOILET("公厕信息"),
        HOSPITAL("医院信息"),
        ENTERTAINMENT_VENUE("娱乐场所信息"),
        PARKING_LOT("停车场信息"),
        ANCIENT_VILLAGE("古村落信息"),
        ANCIENT_VILLAGE2("古村落2信息"),
        SCENIC_AREA("景区信息"),
        TRAVEL_AGENCY("旅行社和网点信息"),
        SHIP_DATA("船舶数据"),
        BOAT_EQUIPMENT("船舶设备对应表");

        private final String description;

        ClientType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 获取指定类型的客户端
     *
     * @param clientType 客户端类型
     * @return 对应的客户端实例
     */
    public Object getClient(ClientType clientType) {
        switch (clientType) {
            case PUBLIC_TOILET:
                return publicToiletClient;
            case HOSPITAL:
                return hospitalClient;
            case ENTERTAINMENT_VENUE:
                return entertainmentVenueClient;
            case PARKING_LOT:
                return parkingLotClient;
            case ANCIENT_VILLAGE:
                return ancientVillageClient;
            case ANCIENT_VILLAGE2:
                return ancientVillage2Client;
            case SCENIC_AREA:
                return scenicAreaClient;
            case TRAVEL_AGENCY:
                return travelAgencyClient;
            case SHIP_DATA:
                return shipDataClient;
            case BOAT_EQUIPMENT:
                return boatEquipmentClient;
            default:
                throw new IllegalArgumentException("不支持的客户端类型: " + clientType);
        }
    }

    /**
     * 获取所有可用的客户端类型
     *
     * @return 客户端类型映射
     */
    public Map<ClientType, String> getAllClientTypes() {
        Map<ClientType, String> clientTypes = new HashMap<>();
        for (ClientType type : ClientType.values()) {
            clientTypes.put(type, type.getDescription());
        }
        return clientTypes;
    }

    /**
     * 检查指定客户端是否可用
     *
     * @param clientType 客户端类型
     * @return true表示可用，false表示不可用
     */
    public boolean isClientAvailable(ClientType clientType) {
        try {
            Object client = getClient(clientType);
            return client != null;
        } catch (Exception e) {
            log.warn("检查客户端可用性失败: {}", clientType, e);
            return false;
        }
    }

    /**
     * 获取客户端状态报告
     *
     * @return 状态报告字符串
     */
    public String getClientStatusReport() {
        StringBuilder report = new StringBuilder();
        report.append("数据客户端状态报告:\n");

        for (ClientType type : ClientType.values()) {
            boolean available = isClientAvailable(type);
            report.append(String.format("- %s (%s): %s\n", 
                    type.name(), 
                    type.getDescription(), 
                    available ? "可用" : "不可用"));
        }

        return report.toString();
    }

    /**
     * 测试所有客户端的连通性
     *
     * @return 测试结果报告
     */
    public String testAllClients() {
        StringBuilder report = new StringBuilder();
        report.append("客户端连通性测试报告:\n");

        for (ClientType type : ClientType.values()) {
            try {
                Object client = getClient(type);
                if (client != null) {
                    // 这里可以添加具体的连通性测试逻辑
                    // 例如调用客户端的测试方法
                    report.append(String.format("- %s: 测试通过\n", type.getDescription()));
                } else {
                    report.append(String.format("- %s: 客户端不可用\n", type.getDescription()));
                }
            } catch (Exception e) {
                report.append(String.format("- %s: 测试失败 - %s\n", type.getDescription(), e.getMessage()));
                log.error("测试客户端失败: {}", type, e);
            }
        }

        return report.toString();
    }
}
