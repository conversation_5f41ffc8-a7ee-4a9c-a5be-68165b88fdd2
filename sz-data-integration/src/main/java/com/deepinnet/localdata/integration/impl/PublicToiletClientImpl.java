package com.deepinnet.localdata.integration.impl;

import com.deepinnet.localdata.integration.PublicToiletClient;
import com.deepinnet.localdata.integration.constants.ActionConstants;
import com.deepinnet.localdata.integration.http.BaseHttpRequestClient;
import com.deepinnet.localdata.integration.http.HttpMethodEnum;
import com.deepinnet.localdata.integration.http.HttpRequestParamNew;
import com.deepinnet.localdata.integration.model.input.PublicToiletQueryDTO;
import com.deepinnet.localdata.integration.model.output.PublicToiletResponseDTO;
import com.deepinnet.localdata.integration.impl.DaPengTokenClientImpl;
import org.apache.commons.collections4.MultiValuedMap;
import org.apache.commons.collections4.multimap.ArrayListValuedHashMap;
import org.springframework.stereotype.Component;

/**
 * 公厕信息Client实现
 * Date: 2024/8/29
 * Author: qoder
 */
@Component
public class PublicToiletClientImpl extends BaseHttpRequestClient implements PublicToiletClient {

    private DaPengTokenClientImpl daPengTokenClient;

    public PublicToiletClientImpl(DaPengTokenClientImpl daPengTokenClient) {
        this.daPengTokenClient = daPengTokenClient;
    }

    @Override
    public PublicToiletResponseDTO getPublicToiletData(PublicToiletQueryDTO queryDTO) {
        System.out.println("开始调用公厕信息接口获取数据，参数: " + queryDTO);

        // 直接在这里替换Token占位符
        String action = ActionConstants.PUBLIC_TOILET_GET_DATA_JSON;
        if (action.contains(ActionConstants.DA_PENG_TOKEN_PLACEHOLDER)) {
            // 获取当前有效的Token
            String token = daPengTokenClient.getCurrentValidToken();
            if (token != null && !token.isEmpty()) {
                // 替换占位符
                action = action.replace(ActionConstants.DA_PENG_TOKEN_PLACEHOLDER, token);
                System.out.println("Token替换完成: " + ActionConstants.PUBLIC_TOILET_GET_DATA_JSON + " -> " + action);
            } else {
                System.out.println("无法获取有效Token，使用原始URL");
            }
        }

        // 构建查询参数
        MultiValuedMap<String, String> paramMap = new ArrayListValuedHashMap<>();
        paramMap.put("pageNo", queryDTO.getPageNo() != null ? queryDTO.getPageNo() : "1");
        paramMap.put("pageSize",
                queryDTO.getPageSize() != null ? queryDTO.getPageSize() : "10");
        if (queryDTO.getSearch() != null) {
            paramMap.put("search", queryDTO.getSearch());
        }

        // 构建HTTP请求参数
        HttpRequestParamNew param = HttpRequestParamNew.builder()
                .method(HttpMethodEnum.GET)
                .action(action)
                .address("http://************:8280") // 数据服务器地址
                .requestParam(paramMap)
                .build();

        PublicToiletResponseDTO publicToiletResponseDTO = httpRequest(param, PublicToiletResponseDTO.class,
                null);
        System.out.println("结束调用公厕信息接口获取数据");
        return publicToiletResponseDTO;
    }

    @Override
    public PublicToiletResponseDTO getPublicToiletData(String pageNo, String pageSize, String search) {
        PublicToiletQueryDTO queryDTO = new PublicToiletQueryDTO();
        queryDTO.setPageNo(pageNo);
        queryDTO.setPageSize(pageSize);
        queryDTO.setSearch(search);
        return getPublicToiletData(queryDTO);
    }

    @Override
    public PublicToiletResponseDTO getPublicToiletData() {
        return getPublicToiletData("1", "10", "");
    }
}