package com.deepinnet.localdata.integration.model.output;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Description: 娱乐场所信息响应DTO
 * Date: 2025/8/27
 * Author: qoder
 */
@Data
public class EntertainmentVenueResponseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 获取数据Json响应
     */
    @JsonProperty("getDataJsonResponse")
    private GetDataJsonResponse getDataJsonResponse;

    @Data
    public static class GetDataJsonResponse implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 返回状态信息（注意：这里是字符串，不是对象）
         */
        @JsonProperty("return")
        private String returnInfo;

        /**
         * 数据内容
         */
        private DataInfo data;
    }

    @Data
    public static class ReturnInfo implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 状态码：1：成功，-1：失败，401：token有误
         */
        private String code;

        /**
         * 状态信息
         */
        private String message;

        /**
         * 是否支持搜索
         */
        @JsonProperty("hasSearch")
        private Boolean hasSearch;

        /**
         * 真实数据类型
         */
        @JsonProperty("realDataType")
        private Integer realDataType;

        /**
         * 错误标识
         */
        @JsonProperty("errorFlag")
        private Integer errorFlag;

        /**
         * 是否为文件
         */
        @JsonProperty("isFile")
        private Boolean isFile;
    }

    @Data
    public static class DataInfo implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 总记录数
         */
        private Integer totalRecords;

        /**
         * 错误标识，0表示正常，1表示错误
         */
        private Integer errorFlag;

        /**
         * 数据结果列表
         */
        private List<VenueRecord> result;
    }

    @Data
    public static class VenueRecord implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 场所ID
         */
        private String venueId;

        /**
         * 场所名称
         */
        private String venueName;

        /**
         * 场所编号
         */
        private String venueCode;

        /**
         * 场所类型
         */
        private String venueType;

        /**
         * 场所类别
         */
        private String venueCategory;

        /**
         * 经营方式
         */
        private String businessMode;

        /**
         * 详细地址
         */
        private String address;

        /**
         * 所属区域
         */
        private String district;

        /**
         * 所属街道
         */
        private String street;

        /**
         * 经度
         */
        private String longitude;

        /**
         * 纬度
         */
        private String latitude;

        /**
         * 联系电话
         */
        private String contactPhone;

        /**
         * 传真号码
         */
        private String faxNumber;

        /**
         * 官方网站
         */
        private String officialWebsite;

        /**
         * 电子邮箱
         */
        private String email;

        /**
         * 邮政编码
         */
        private String postalCode;

        /**
         * 开业年份
         */
        private String establishedYear;

        /**
         * 法人代表
         */
        private String legalRepresentative;

        /**
         * 经营者姓名
         */
        private String operatorName;

        /**
         * 经营面积
         */
        private String businessArea;

        /**
         * 员工人数
         */
        private String staffCount;

        /**
         * 容纳人数
         */
        private String capacity;

        /**
         * 营业时间
         */
        private String operatingHours;

        /**
         * 是否24小时营业
         */
        private String is24Hours;

        /**
         * 经营许可证号
         */
        private String businessLicenseNumber;

        /**
         * 许可证有效期
         */
        private String licenseExpiryDate;

        /**
         * 消防许可证号
         */
        private String fireLicenseNumber;

        /**
         * 卫生许可证号
         */
        private String healthLicenseNumber;

        /**
         * 文化经营许可证号
         */
        private String cultureLicenseNumber;

        /**
         * 主要经营项目
         */
        private String mainBusinessItems;

        /**
         * 服务特色
         */
        private String serviceFeatures;

        /**
         * 设施设备
         */
        private String facilities;

        /**
         * 消费标准
         */
        private String consumptionStandard;

        /**
         * 价格区间
         */
        private String priceRange;

        /**
         * 停车位数量
         */
        private String parkingSpaces;

        /**
         * 是否有停车场
         */
        private String hasParkingLot;

        /**
         * 交通便利程度
         */
        private String transportationAccess;

        /**
         * 周边环境描述
         */
        private String surroundingEnvironment;

        /**
         * 安全措施
         */
        private String safetyMeasures;

        /**
         * 是否有监控设备
         */
        private String hasMonitoring;

        /**
         * 应急预案
         */
        private String emergencyPlan;

        /**
         * 运营状态
         */
        private String operationStatus;

        /**
         * 服务质量等级
         */
        private String serviceGrade;

        /**
         * 客户满意度
         */
        private String customerSatisfaction;

        /**
         * 投诉建议
         */
        private String complaints;

        /**
         * 违法违规记录
         */
        private String violationRecords;

        /**
         * 行政处罚情况
         */
        private String penaltyRecords;

        /**
         * 监管部门
         */
        private String regulatoryAuthority;

        /**
         * 监督电话
         */
        private String supervisionPhone;

        /**
         * 年检情况
         */
        private String annualInspection;

        /**
         * 信用等级
         */
        private String creditRating;

        /**
         * 荣誉奖项
         */
        private String honors;

        /**
         * 数据来源
         */
        private String dataSource;

        /**
         * 创建时间
         */
        private String createTime;

        /**
         * 更新时间
         */
        private String updateTime;

        /**
         * 备注
         */
        private String remark;
    }
}