package com.deepinnet.localdata.integration.model.output;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Description: 古村落2信息响应DTO
 * Date: 2025/8/27
 * Author: qoder
 */
@Data
public class AncientVillage2ResponseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 获取数据Json响应
     */
    @JsonProperty("getDataJsonResponse")
    private GetDataJsonResponse getDataJsonResponse;

    @Data
    public static class GetDataJsonResponse implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 返回状态信息（注意：这里是字符串，不是对象）
         */
        @JsonProperty("return")
        private String returnInfo;

        /**
         * 数据内容
         */
        private DataInfo data;
    }

    @Data
    public static class ReturnInfo implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 状态码
         */
        private String code;

        /**
         * 状态信息
         */
        private String message;

        /**
         * 是否支持搜索
         */
        private Boolean hasSearch;

        /**
         * 真实数据类型
         */
        private Integer realDataType;

        /**
         * 错误标识
         */
        private Integer errorFlag;

        /**
         * 是否为文件
         */
        private Boolean isFile;
    }

    @Data
    public static class DataInfo implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 总记录数
         */
        private Integer totalRecords;

        /**
         * 错误标识，0表示正常，1表示错误
         */
        private Integer errorFlag;

        /**
         * 数据结果列表
         */
        private List<AncientVillage2Record> result;
    }

    @Data
    public static class AncientVillage2Record implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 古村落ID
         */
        private String villageId;

        /**
         * 古村落名称
         */
        private String villageName;

        /**
         * 古村落编号
         */
        private String villageCode;

        /**
         * 古村落类型
         */
        private String villageType;

        /**
         * 古村落等级
         */
        private String villageLevel;

        /**
         * 古村落分类
         */
        private String villageCategory;

        /**
         * 所在省份
         */
        private String province;

        /**
         * 所在城市
         */
        private String city;

        /**
         * 所在区县
         */
        private String district;

        /**
         * 所在街道/乡镇
         */
        private String street;

        /**
         * 详细地址
         */
        private String detailedAddress;

        /**
         * 经度
         */
        private String longitude;

        /**
         * 纬度
         */
        private String latitude;

        /**
         * 建村年代
         */
        private String establishmentYear;

        /**
         * 历史年代
         */
        private String historicalPeriod;

        /**
         * 村落面积（平方公里）
         */
        private String villageArea;

        /**
         * 人口数量
         */
        private String population;

        /**
         * 户数
         */
        private String householdCount;

        /**
         * 主要民族
         */
        private String mainEthnic;

        /**
         * 主要语言
         */
        private String mainLanguage;

        /**
         * 建筑风格
         */
        private String architecturalStyle;

        /**
         * 主要建筑材料
         */
        private String mainBuildingMaterials;

        /**
         * 古建筑数量
         */
        private String ancientBuildingCount;

        /**
         * 文物保护单位级别
         */
        private String culturalRelicLevel;

        /**
         * 文物保护编号
         */
        private String culturalRelicNumber;

        /**
         * 历史文化价值
         */
        private String historicalCulturalValue;

        /**
         * 传统文化特色
         */
        private String traditionalCulturalFeatures;

        /**
         * 民俗活动
         */
        private String folkActivities;

        /**
         * 传统手工艺
         */
        private String traditionalCrafts;

        /**
         * 特色美食
         */
        private String specialtyFood;

        /**
         * 方言特色
         */
        private String dialectCharacteristics;

        /**
         * 旅游开发状况
         */
        private String tourismDevelopmentStatus;

        /**
         * 年游客量
         */
        private String annualVisitorCount;

        /**
         * 门票价格
         */
        private String ticketPrice;

        /**
         * 开放时间
         */
        private String openingHours;

        /**
         * 最佳游览季节
         */
        private String bestVisitingSeason;

        /**
         * 主要景点
         */
        private String majorAttractions;

        /**
         * 交通便利程度
         */
        private String transportationAccessibility;

        /**
         * 住宿设施
         */
        private String accommodationFacilities;

        /**
         * 餐饮设施
         */
        private String diningFacilities;

        /**
         * 购物设施
         */
        private String shoppingFacilities;

        /**
         * 医疗设施
         */
        private String medicalFacilities;

        /**
         * 通信设施
         */
        private String communicationFacilities;

        /**
         * 基础设施完善程度
         */
        private String infrastructureCompleteness;

        /**
         * 环境保护状况
         */
        private String environmentalProtectionStatus;

        /**
         * 生态环境质量
         */
        private String ecologicalEnvironmentQuality;

        /**
         * 污染治理情况
         */
        private String pollutionControlSituation;

        /**
         * 绿化覆盖率
         */
        private String greenCoverageRate;

        /**
         * 水资源状况
         */
        private String waterResourceStatus;

        /**
         * 保护措施
         */
        private String protectionMeasures;

        /**
         * 开发规划
         */
        private String developmentPlan;

        /**
         * 管理机构
         */
        private String managementInstitution;

        /**
         * 负责人姓名
         */
        private String responsiblePersonName;

        /**
         * 联系电话
         */
        private String contactPhone;

        /**
         * 电子邮箱
         */
        private String email;

        /**
         * 官方网站
         */
        private String officialWebsite;

        /**
         * 荣誉称号
         */
        private String honoraryTitles;

        /**
         * 获奖情况
         */
        private String awardStatus;

        /**
         * 媒体报道
         */
        private String mediaReports;

        /**
         * 知名度
         */
        private String popularity;

        /**
         * 发展前景
         */
        private String developmentProspects;

        /**
         * 存在问题
         */
        private String existingProblems;

        /**
         * 改进建议
         */
        private String improvementSuggestions;

        /**
         * 备注
         */
        private String remarks;

        /**
         * 数据更新时间
         */
        private String updateTime;

        /**
         * 数据来源
         */
        private String dataSource;

        /**
         * 数据状态
         */
        private String dataStatus;
    }
}