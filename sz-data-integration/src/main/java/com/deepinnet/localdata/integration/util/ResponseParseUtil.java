package com.deepinnet.localdata.integration.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.deepinnet.localdata.integration.model.output.BaseDataResponseDTO;
import lombok.extern.slf4j.Slf4j;

/**
 * Description: 响应解析工具类
 * 用于解析大鹏接口的标准响应格式
 * Date: 2025/9/8
 * Author: qoder
 */
@Slf4j
public class ResponseParseUtil {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 解析returnInfo字符串为ReturnInfo对象
     *
     * @param returnInfoStr returnInfo字符串
     * @return 解析后的ReturnInfo对象，解析失败返回null
     */
    public static BaseDataResponseDTO.ReturnInfo parseReturnInfo(String returnInfoStr) {
        if (returnInfoStr == null || returnInfoStr.trim().isEmpty()) {
            return null;
        }

        try {
            JsonNode jsonNode = objectMapper.readTree(returnInfoStr);
            BaseDataResponseDTO.ReturnInfo returnInfo = new BaseDataResponseDTO.ReturnInfo();

            // 解析code字段
            if (jsonNode.has("code")) {
                JsonNode codeNode = jsonNode.get("code");
                returnInfo.setCode(codeNode.isTextual() ? codeNode.asText() : String.valueOf(codeNode.asInt()));
            }

            // 解析message字段
            if (jsonNode.has("message")) {
                returnInfo.setMessage(jsonNode.get("message").asText());
            }

            // 解析hasSearch字段
            if (jsonNode.has("hasSearch")) {
                returnInfo.setHasSearch(jsonNode.get("hasSearch").asBoolean());
            }

            // 解析realDataType字段
            if (jsonNode.has("realDataType")) {
                returnInfo.setRealDataType(jsonNode.get("realDataType").asInt());
            }

            // 解析errorFlag字段
            if (jsonNode.has("errorFlag")) {
                returnInfo.setErrorFlag(jsonNode.get("errorFlag").asInt());
            }

            // 解析isFile字段
            if (jsonNode.has("isFile")) {
                returnInfo.setIsFile(jsonNode.get("isFile").asBoolean());
            }

            return returnInfo;

        } catch (Exception e) {
            log.warn("解析returnInfo字符串失败: {}", returnInfoStr, e);
            return null;
        }
    }

    /**
     * 判断响应是否成功
     *
     * @param returnInfoStr returnInfo字符串
     * @return true表示成功，false表示失败
     */
    public static boolean isSuccess(String returnInfoStr) {
        if (returnInfoStr == null || returnInfoStr.trim().isEmpty()) {
            return false;
        }

        // 简单的字符串匹配判断成功状态
        return returnInfoStr.contains("\"code\":1") || 
               returnInfoStr.contains("\"code\":\"1\"") ||
               returnInfoStr.contains("\"code\": 1") ||
               returnInfoStr.contains("\"code\": \"1\"");
    }

    /**
     * 从returnInfo字符串中提取错误信息
     *
     * @param returnInfoStr returnInfo字符串
     * @return 错误信息，如果没有错误或解析失败返回null
     */
    public static String extractErrorMessage(String returnInfoStr) {
        BaseDataResponseDTO.ReturnInfo returnInfo = parseReturnInfo(returnInfoStr);
        if (returnInfo != null && !"1".equals(returnInfo.getCode())) {
            return returnInfo.getMessage();
        }
        return null;
    }

    /**
     * 验证响应数据的完整性
     *
     * @param response 响应对象
     * @return true表示数据完整，false表示数据不完整
     */
    public static boolean validateResponse(BaseDataResponseDTO<?> response) {
        if (response == null) {
            log.warn("响应对象为null");
            return false;
        }

        if (response.getGetDataJsonResponse() == null) {
            log.warn("getDataJsonResponse为null");
            return false;
        }

        if (response.getGetDataJsonResponse().getReturnInfo() == null) {
            log.warn("returnInfo为null");
            return false;
        }

        if (response.getGetDataJsonResponse().getData() == null) {
            log.warn("data为null");
            return false;
        }

        return true;
    }

    /**
     * 获取响应的摘要信息
     *
     * @param response 响应对象
     * @return 摘要信息字符串
     */
    public static String getResponseSummary(BaseDataResponseDTO<?> response) {
        if (!validateResponse(response)) {
            return "响应数据不完整";
        }

        StringBuilder summary = new StringBuilder();
        summary.append("响应摘要: ");

        // 成功状态
        summary.append("成功=").append(response.isSuccess());

        // 总记录数
        Integer totalRecords = response.getTotalRecords();
        if (totalRecords != null) {
            summary.append(", 总记录数=").append(totalRecords);
        }

        // 分页信息
        Integer pageNo = response.getPageNo();
        Integer pageSize = response.getPageSize();
        if (pageNo != null && pageSize != null) {
            summary.append(", 页号=").append(pageNo).append(", 页面大小=").append(pageSize);
        }

        // 结果数量
        if (response.getResultList() != null) {
            summary.append(", 返回条数=").append(response.getResultList().size());
        }

        return summary.toString();
    }
}
