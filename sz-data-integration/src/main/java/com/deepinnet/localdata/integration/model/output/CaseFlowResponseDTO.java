package com.deepinnet.localdata.integration.model.output;

import lombok.Data;
import java.util.List;

/**
 * 工单流程查询响应DTO
 * 根据API文档 2.1.6 工单流程查询接口规范
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
public class CaseFlowResponseDTO {

    /**
     * 自定义数据
     */
    private Custom custom;

    /**
     * 状态信息
     */
    private Status status;

    @Data
    public static class Custom {
        /**
         * 流程列表
         */
        private List<FlowItem> list;
    }

    @Data
    public static class FlowItem {
        /**
         * 处理单位
         */
        private String handledept;

        /**
         * 处理人
         */
        private String handleuser;

        /**
         * 处理人电话(可选)
         */
        private String handleuserphone;

        /**
         * 接收时间
         */
        private String sendtime;

        /**
         * 处理时间
         */
        private String finishtime;

        /**
         * 办理意见
         */
        private String opinion;

        /**
         * 回复意见
         */
        private String answeropinion;

        /**
         * 操作内容(分镜至xxx部门)
         */
        private String handlelog;

        /**
         * 操作名称
         */
        private String actionname;

        /**
         * 附件数组
         */
        private List<FileAttachment> files;
    }

    @Data
    public static class FileAttachment {
        /**
         * 附件名称
         */
        private String attachname;

        /**
         * 附件URL
         */
        private String attachurl;
    }

    @Data
    public static class Status {
        /**
         * 状态码
         */
        private String code;

        /**
         * 状态描述
         */
        private String text;
    }

    /**
     * 创建成功响应
     */
    public static CaseFlowResponseDTO success(List<FlowItem> flowList) {
        CaseFlowResponseDTO response = new CaseFlowResponseDTO();

        Custom custom = new Custom();
        custom.setList(flowList);
        response.setCustom(custom);

        Status status = new Status();
        status.setCode("1");
        status.setText("请求成功");
        response.setStatus(status);

        return response;
    }

    /**
     * 创建失败响应
     */
    public static CaseFlowResponseDTO error(String errorMessage) {
        CaseFlowResponseDTO response = new CaseFlowResponseDTO();

        Custom custom = new Custom();
        custom.setList(null);
        response.setCustom(custom);

        Status status = new Status();
        status.setCode("0");
        status.setText(errorMessage != null ? errorMessage : "请求失败");
        response.setStatus(status);

        return response;
    }
}