package com.deepinnet.localdata.integration.model.output;

import lombok.Data;

/**
 * OAuth2 Token响应DTO
 */
@Data
public class OAuth2TokenResponseDTO {

    /**
     * 状态信息
     */
    private Status status;

    /**
     * 自定义数据
     */
    private Custom custom;

    @Data
    public static class Status {
        /**
         * 状态码
         */
        private String code;
    }

    @Data
    public static class Custom {
        /**
         * 访问令牌
         */
        private String accessToken;

        /**
         * 过期时间(秒)
         */
        private String expiresIn;
    }

    /**
     * 创建成功响应
     */
    public static OAuth2TokenResponseDTO success(String accessToken, String expiresIn) {
        OAuth2TokenResponseDTO response = new OAuth2TokenResponseDTO();

        Status status = new Status();
        status.setCode("1");
        response.setStatus(status);

        Custom custom = new Custom();
        custom.setAccessToken(accessToken);
        custom.setExpiresIn(expiresIn);
        response.setCustom(custom);

        return response;
    }

    /**
     * 创建失败响应
     */
    public static OAuth2TokenResponseDTO error(String code) {
        OAuth2TokenResponseDTO response = new OAuth2TokenResponseDTO();

        Status status = new Status();
        status.setCode(code);
        response.setStatus(status);

        return response;
    }
}