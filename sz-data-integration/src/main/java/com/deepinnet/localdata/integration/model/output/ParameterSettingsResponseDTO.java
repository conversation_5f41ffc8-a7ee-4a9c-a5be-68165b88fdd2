package com.deepinnet.localdata.integration.model.output;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Description: 参数设置信息响应DTO
 * Date: 2025/8/27
 * Author: qoder
 */
@Data
public class ParameterSettingsResponseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 获取数据Json响应
     */
    @JsonProperty("getDataJsonResponse")
    private GetDataJsonResponse getDataJsonResponse;

    @Data
    public static class GetDataJsonResponse implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 返回状态信息（注意：这里是字符串，不是对象）
         */
        @JsonProperty("return")
        private String returnInfo;

        /**
         * 数据内容
         */
        private DataInfo data;
    }

    @Data
    public static class DataInfo implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 总记录数
         */
        private Integer totalRecords;

        /**
         * 错误标识，0表示正常，1表示错误
         */
        private Integer errorFlag;

        /**
         * 数据结果列表
         */
        private List<ParameterRecord> result;
    }

    @Data
    public static class ParameterRecord implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 参数设置ID
         */
        private String parameterId;

        /**
         * 参数分类
         */
        private String parameterCategory;

        /**
         * 参数名称
         */
        private String parameterName;

        /**
         * 参数键名
         */
        private String parameterKey;

        /**
         * 参数值
         */
        private String parameterValue;

        /**
         * 参数类型
         */
        private String parameterType;

        /**
         * 参数描述
         */
        private String parameterDescription;

        /**
         * 默认值
         */
        private String defaultValue;

        /**
         * 是否必填
         */
        private String isRequired;

        /**
         * 是否启用
         */
        private String isEnabled;

        /**
         * 参数范围/约束
         */
        private String parameterRange;

        /**
         * 参数单位
         */
        private String parameterUnit;

        /**
         * 参数格式
         */
        private String parameterFormat;

        /**
         * 验证规则
         */
        private String validationRule;

        /**
         * 参数组
         */
        private String parameterGroup;

        /**
         * 排序序号
         */
        private String sortOrder;

        /**
         * 系统参数标识
         */
        private String isSystemParam;

        /**
         * 用户可修改标识
         */
        private String isUserEditable;

        /**
         * 参数级别
         */
        private String parameterLevel;

        /**
         * 生效范围
         */
        private String effectiveScope;

        /**
         * 配置环境
         */
        private String configEnvironment;

        /**
         * 依赖参数
         */
        private String dependentParameters;

        /**
         * 参数版本
         */
        private String parameterVersion;

        /**
         * 最后修改人
         */
        private String lastModifiedBy;

        /**
         * 最后修改时间
         */
        private String lastModifiedTime;

        /**
         * 创建人
         */
        private String createdBy;

        /**
         * 创建时间
         */
        private String createdTime;

        /**
         * 参数来源
         */
        private String parameterSource;

        /**
         * 配置文件路径
         */
        private String configFilePath;

        /**
         * 备份值
         */
        private String backupValue;

        /**
         * 历史值
         */
        private String historyValue;

        /**
         * 生效时间
         */
        private String effectiveTime;

        /**
         * 失效时间
         */
        private String expireTime;

        /**
         * 应用范围
         */
        private String applicationScope;

        /**
         * 模块名称
         */
        private String moduleName;

        /**
         * 功能名称
         */
        private String functionName;

        /**
         * 权限要求
         */
        private String permissionRequired;

        /**
         * 审核状态
         */
        private String auditStatus;

        /**
         * 审核人
         */
        private String auditor;

        /**
         * 审核时间
         */
        private String auditTime;

        /**
         * 审核意见
         */
        private String auditComment;

        /**
         * 备注
         */
        private String remark;
    }
}