package com.deepinnet.localdata.integration.error;

import com.deepinnet.digitaltwin.common.error.ErrorLevel;
import com.deepinnet.digitaltwin.common.error.ErrorType;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> wong
 * @create 2024/7/29 15:15
 * @Description
 */
@AllArgsConstructor
@Getter
public enum BizErrorCode {


    /*------------------------------------------------------------------------*/
    /*                        通用事件[0000开头]                                  */
    /*------------------------------------------------------------------------*/

    /**
     * 未知异常
     */
    UNKNOWN_EXCEPTION(ErrorLevel.ERROR, ErrorType.SYSTEM, "0000000", "未知异常"),

    /**
     * 请求参数非法
     */
    ILLEGAL_PARAMS(ErrorLevel.ERROR, ErrorType.BIZ, "0000001", "参数错误"),


    /**
     * 并发操作
     */
    OPERATION_CONCURRENT_EXCP(ErrorLevel.ERROR, ErrorType.BIZ, "0000003", "并发处理异常"),

    /**
     * 系统暂时不支持
     */
    SYSTEM_NOT_SUPPORT(ErrorLevel.ERROR, ErrorType.SYSTEM, "0000004", "系统暂时不支持"),

    NETWORK_REQUEST_ERROR(ErrorLevel.ERROR, ErrorType.SYSTEM, "0000005", "网络请求异常"),

    /**
     * 读取文件失败
     */
    READ_FILE_ERROR(ErrorLevel.ERROR, ErrorType.BIZ, "0000006", "读取数据错误"),

    /**
     * 数据异常
     */
    DATA_INVALID(ErrorLevel.ERROR, ErrorType.BIZ, "0000007", "数据异常"),

    /**
     * 业务幂等
     **/
    IDEMPOTENT_REQUEST_ERROR(ErrorLevel.ERROR, ErrorType.BIZ, "0000995", "业务幂等"),

    /**
     * 非法请求
     **/
    ILLEGAL_REQUEST(ErrorLevel.ERROR, ErrorType.BIZ, "0000996", "非法请求"),

    /**
     * 服务熔断不可用
     */
    SERVICE_FUSING_NOT_AVAILABLE(ErrorLevel.WARN, ErrorType.SYSTEM, "0000997", "服务熔断不可用"),

    /**
     * 系统限流
     */
    SLA_LIMIT_ERROR(ErrorLevel.WARN, ErrorType.SYSTEM, "0000998", "SLA限流异常"),

    /**
     * 系统内部错误
     */
    INTERNAL_SERVER_ERROR(ErrorLevel.ERROR, ErrorType.SYSTEM, "0000999", "系统开小差了"),


    /*------------------------------------------------------------------------*/
    /*                        交保任务事件[1000开头]                             */
    /*------------------------------------------------------------------------*/

    TASK_ROUTE_NAME_DUPLICATED(ErrorLevel.ERROR, ErrorType.BIZ, "1000030", "线路名称重复，请重新命名"),

    TRAFFIC_SECURITY_TASK_CANNOT_MODIFY_THE_ROUTE_IN_THE_CURRENT_STATUS(ErrorLevel.ERROR, ErrorType.BIZ, "1000040", "交保任务无法在当前状态下修改线路"),


    /*------------------------------------------------------------------------*/
    /*                        交通事件[2000开头]                             */
    /*------------------------------------------------------------------------*/

    QUERY_ERROR(ErrorLevel.ERROR, ErrorType.BIZ, "2000001", "查询交通事件接口失败"),

    /**
     * 实时人流查询失败
     */
    REAL_TIME_PEOPLE_FLOW_QUERY_ERROR(ErrorLevel.ERROR, ErrorType.BIZ, "2000002", "实时人流查询接口查询失败"),

    TRY_IMPORT_FILE_ERROR(ErrorLevel.ERROR, ErrorType.BIZ, "2000003", "导入文件失败，请稍后重试"),

    /**
     * 网格人流数据查询失败
     */
    GRID_PEOPLE_FLOW_QUERY_ERROR(ErrorLevel.ERROR, ErrorType.BIZ, "2000004", "网格人流数据查询失败"),

    AREA_NOT_FOUND(ErrorLevel.ERROR, ErrorType.BIZ, "2000005", "区域不存在"),

    /**
     * 人流来源地查询失败
     */
    PEOPLE_FLOW_SOURCE_QUERY_ERROR(ErrorLevel.ERROR, ErrorType.BIZ, "2000006", "人流来源地查询失败"),



    /*------------------------------------------------------------------------*/
    /*                        组织架构[3000开头]                             */
    /*------------------------------------------------------------------------*/

    CONFIRM_IMPORT_ERROR(ErrorLevel.ERROR, ErrorType.BIZ, "3000001", "确认导入失败"),

    CANCEL_IMPORT_ERROR(ErrorLevel.ERROR, ErrorType.BIZ, "3000002", "取消导入失败"),

    NOT_FOUND_TASK(ErrorLevel.ERROR, ErrorType.BIZ, "2003", "任务不存在"),

    /*------------------------------------------------------------------------*/
    /*                        对外请求[4000开头]                             */
    /*------------------------------------------------------------------------*/

    INVOKE_DAHUA_API_ERROR(ErrorLevel.ERROR, ErrorType.BIZ, "4000001", "调用大华请求失败"),

    DEVICE_NOT_EXIST(ErrorLevel.ERROR, ErrorType.BIZ, "4000002", "设备不存在"),

    INVOKE_HAIKANG_API_ERROR(ErrorLevel.ERROR, ErrorType.BIZ, "4000003", "调用海康请求失败"),

    INVOKE_DP_POLICE_CENTER_ERROR(ErrorLevel.ERROR, ErrorType.BIZ, "4000004", "调用大鹏警情中心失败"),

    INVOKE_YUANFEI_API_ERROR(ErrorLevel.ERROR, ErrorType.BIZ, "4000005", "调用鸢飞请求失败"),

    INVOKE_GAODE_API_ERROR(ErrorLevel.ERROR, ErrorType.BIZ, "4000006", "调用高德企业地图请求失败");

    /**
     * 错误级别
     */
    private ErrorLevel errorLevel;

    /**
     * 错误类型
     */
    private ErrorType errorType;

    /**
     * 错误编码
     */
    private String code;

    /**
     * 错误描述
     */
    private String desc;
}
