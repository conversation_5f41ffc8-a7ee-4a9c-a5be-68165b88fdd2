package com.deepinnet.localdata.integration.model.output;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Description: 古村落2信息真实响应DTO（匹配大鹏系统实际返回的数据结构）
 * Date: 2025/9/1
 * Author: qoder
 */
@Data
public class AncientVillage2RealResponseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 获取数据Json响应
     */
    @JsonProperty("getDataJsonResponse")
    private GetDataJsonResponse getDataJsonResponse;

    @Data
    public static class GetDataJsonResponse implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 返回状态信息（注意：这里是字符串，包含嵌套的JSON对象）
         */
        @JsonProperty("return")
        private String returnInfo;

        /**
         * 错误标识，0表示正常，1表示错误
         */
        @JsonProperty("errorFlag")
        private Integer errorFlag;

        /**
         * 是否为文件
         */
        @JsonProperty("isFile")
        private Boolean isFile;

        /**
         * 数据内容
         */
        private DataInfo data;
    }

    @Data
    public static class ReturnInfo implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 状态码：1：成功，-1：失败，401：token有误
         */
        private String code;

        /**
         * 状态信息
         */
        private String message;

        /**
         * 是否支持搜索
         */
        @JsonProperty("hasSearch")
        private Boolean hasSearch;

        /**
         * 真实数据类型
         */
        @JsonProperty("realDataType")
        private Integer realDataType;

        /**
         * 数据内容（当返回的是完整响应时）
         */
        private Object data;

        /**
         * 错误标识，0表示正常，1表示错误
         */
        @JsonProperty("errorFlag")
        private Integer errorFlag;

        /**
         * 是否为文件
         */
        @JsonProperty("isFile")
        private Boolean isFile;
    }

    @Data
    public static class DataInfo implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 总记录数
         */
        private Integer totalRecords;

        /**
         * 页号
         */
        private Integer pageNo;

        /**
         * 页面大小
         */
        private Integer pageSize;

        /**
         * 数据结果列表
         */
        private List<AncientVillage2Record> result;

        /**
         * 标题信息
         */
        private List<TitleInfo> title;

        /**
         * 是否可分页
         */
        private Boolean isPageable;
    }

    @Data
    public static class AncientVillage2Record implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 古村落的特色景点
         */
        private String special_sceni_cspot;

        /**
         * 删除时间
         */
        private String delete_time;

        /**
         * 古村落的旅游活动项目
         */
        private String activity_items;

        /**
         * 经度
         */
        private String lon;

        /**
         * 古村落的开放时间
         */
        private String business_hours;

        /**
         * 纬度
         */
        private String lat;

        /**
         * 古村落的门票价格
         */
        private String tickets_price;

        /**
         * 分区
         */
        private String data_dt_iso;

        /**
         * 古村落的最佳游览季节
         */
        private String best_tourist_season;

        /**
         * id
         */
        private String id;

        /**
         * 相邻景区的交通通达性
         */
        private String traffic_accessibility;

        /**
         * 古村落名称
         */
        private String name;

        /**
         * 古村落管理机构的名称
         */
        private String manage_name;

        /**
         * 古村落的概况
         */
        private String general_situation;

        /**
         * 数据来源
         */
        private String data_source;

        /**
         * 古村落的交通状况
         */
        private String traffic_condition;

        /**
         * 创建时间
         */
        private String time_create;

        /**
         * 古村落的图像资料
         */
        private String image_data;

        /**
         * 更新时间
         */
        private String update_time;

        /**
         * 古村落的分布图
         */
        private String distribution;

        /**
         * 备注
         */
        private String remarks;

        /**
         * 古村落的构成
         */
        private String constitute;

        /**
         * 预留字段1
         */
        private String field1;

        /**
         * 古村落的开发状况
         */
        private String development_situation;

        /**
         * 预留字段2
         */
        private String field2;

        /**
         * 预留字段3
         */
        private String field3;
    }

    @Data
    public static class TitleInfo implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 数据代码
         */
        private String dataCode;

        /**
         * 数据名称
         */
        private String dataName;

        /**
         * 标准类型
         */
        private String standardType;

        /**
         * 数据类型
         */
        private String dataType;

        /**
         * 过滤类型
         */
        private String filterType;

        /**
         * 共享类型
         */
        private String shareKind;

        /**
         * 是否有效
         */
        private Boolean valid;

        /**
         * 字典表
         */
        private String dicTable;
    }
}