package com.deepinnet.localdata.integration.model.input;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Description: 设备报警信息查询请求参数DTO
 * Date: 2025/8/26
 * Author: qoder
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeviceAlarmQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 分页参数，页号
     */
    private String pageNo;

    /**
     * 分页参数，一页显示的数量，一页最大100条
     */
    private String pageSize;

    /**
     * 高级搜索参数，json字符串，如果没有传空字符串
     */
    private String search;

    /**
     * 动态访问令牌（用于用户身份认证，可选）
     * 注意：这与URL路径中的服务标识符不同
     */
    private String token;

    /**
     * 构造函数，设置默认值
     */
    public DeviceAlarmQueryDTO(String pageNo, String pageSize, String search) {
        this.pageNo = pageNo != null ? pageNo : "1";
        this.pageSize = pageSize != null ? pageSize : "10";
        this.search = search != null ? search : "";
    }
}