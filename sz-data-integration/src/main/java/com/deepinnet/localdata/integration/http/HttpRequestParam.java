package com.deepinnet.localdata.integration.http;

import com.deepinnet.localdata.integration.constants.ActionConstants;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * Description: http请求参数
 * 已废弃，推荐使用 {@link HttpRequestParamNew}
 *
 * Date: 2024/8/28
 * Author: lijunheng
 */
@Data
@Builder
@Deprecated
public class HttpRequestParam implements Serializable {

    /**
     * 地址前缀，例如http://www.baidu.com
     */
    private String address;

    /**
     * 行为，就是调用请求URL
     * {@link ActionConstants}
     */
    private String action;

    private HttpMethodEnum method;

    private String requestHeader;

    private String requestBody;

    private String requestParam;


}
