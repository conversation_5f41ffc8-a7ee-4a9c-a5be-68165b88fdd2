package com.deepinnet.localdata.integration.util;

import org.apache.commons.lang3.StringUtils;

import javax.crypto.Cipher;
import java.io.UnsupportedEncodingException;
import java.security.*;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

/**
 * Description:
 * Date: 2024/8/28
 * Author: lijunheng
 */
public class CryptoUtils {
    private static final String RSA_ALGORITHM = "RSA";
    private static final String SHA256_WITH_RSA_SIGNATURE_ALGORITHM = "SHA256withRSA";
    private static final String SHA256_ENCODE_ALGORITHM = "SHA-256";
    private static final String UTF8 = "utf-8";

    public static RSAKey generateRSAKey() {
        try {
            KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance(RSA_ALGORITHM);
            keyPairGenerator.initialize(2048);
            KeyPair keyPair = keyPairGenerator.generateKeyPair();
            RSAPublicKey publicKey = (RSAPublicKey) keyPair.getPublic();
            RSAPrivateKey privateKey = (RSAPrivateKey) keyPair.getPrivate();
            RSAKey rsaKey = new RSAKey();
            rsaKey.setPublicKey(Base64.getEncoder().encodeToString(publicKey.getEncoded()));
            rsaKey.setPrivateKey(Base64.getEncoder().encodeToString(privateKey.getEncoded()));
            return rsaKey;
        } catch (NoSuchAlgorithmException var5) {
            throw new RuntimeException(var5);
        }
    }

    public static boolean verifySignBySHA256WithRSA(String publicKey, String plainData, String signedString) {
        boolean signedSuccess = false;

        try {
            MessageDigest messageDigest = MessageDigest.getInstance(SHA256_ENCODE_ALGORITHM);
            messageDigest.update(plainData.getBytes(UTF8));
            byte[] outputDigestVerify = messageDigest.digest();
            Signature verifySign = Signature.getInstance(SHA256_WITH_RSA_SIGNATURE_ALGORITHM);
            verifySign.initVerify(restorePublicKey(publicKey));
            verifySign.update(outputDigestVerify);
            signedSuccess = verifySign.verify(Base64.getDecoder().decode(signedString));
            return signedSuccess;
        } catch (InvalidKeyException | SignatureException | UnsupportedEncodingException |
                 NoSuchAlgorithmException var7) {
            throw new RuntimeException(var7);
        }
    }

    public static String generateSignBySHA256WithRSA(String privateKey, String plainData) {
        String signedText = null;

        try {
            MessageDigest messageDigest = MessageDigest.getInstance(SHA256_ENCODE_ALGORITHM);
            messageDigest.update(plainData.getBytes(UTF8));
            byte[] outputDigestSign = messageDigest.digest();
            Signature signature = Signature.getInstance(SHA256_WITH_RSA_SIGNATURE_ALGORITHM);
            signature.initSign(restorePrivateKey(privateKey));
            signature.update(outputDigestSign);
            byte[] signed = signature.sign();
            signedText = Base64.getEncoder().encodeToString(signed);
            return signedText;
        } catch (InvalidKeyException | SignatureException | UnsupportedEncodingException |
                 NoSuchAlgorithmException var7) {
            throw new RuntimeException(var7);
        }
    }

    public static PublicKey restorePublicKey(String publicKey) {
        X509EncodedKeySpec x509EncodedKeySpec = new X509EncodedKeySpec(Base64.getDecoder().decode(publicKey));

        try {
            KeyFactory factory = KeyFactory.getInstance(RSA_ALGORITHM);
            return factory.generatePublic(x509EncodedKeySpec);
        } catch (InvalidKeySpecException | NoSuchAlgorithmException var3) {
            throw new RuntimeException(var3);
        }
    }

    public static PrivateKey restorePrivateKey(String privateKey) {
        PKCS8EncodedKeySpec pkcs8EncodedKeySpec = new PKCS8EncodedKeySpec(Base64.getDecoder().decode(privateKey));

        try {
            KeyFactory factory = KeyFactory.getInstance(RSA_ALGORITHM);
            return factory.generatePrivate(pkcs8EncodedKeySpec);
        } catch (InvalidKeySpecException | NoSuchAlgorithmException var3) {
            throw new RuntimeException(var3);
        }
    }

    public static String encryptWithRSA2PublicKey(String publicKey, String plainData) {
        try {
            byte[] decoded = Base64.getDecoder().decode(publicKey);
            RSAPublicKey pubKey = (RSAPublicKey) KeyFactory.getInstance(RSA_ALGORITHM).generatePublic(new X509EncodedKeySpec(decoded));
            Cipher cipher = Cipher.getInstance(RSA_ALGORITHM);
            cipher.init(1, pubKey);
            String outStr = Base64.getEncoder().encodeToString(cipher.doFinal(plainData.getBytes(UTF8)));
            return outStr;
        } catch (Exception var6) {
            throw new RuntimeException(var6);
        }
    }

    public static String decryptWithRSA2PrivateKey(String privateKey, String encryptData) {
        try {
            byte[] inputByte = Base64.getDecoder().decode(encryptData);
            byte[] decoded = Base64.getDecoder().decode(privateKey);
            RSAPrivateKey priKey = (RSAPrivateKey) KeyFactory.getInstance(RSA_ALGORITHM).generatePrivate(new PKCS8EncodedKeySpec(decoded));
            Cipher cipher = Cipher.getInstance(RSA_ALGORITHM);
            cipher.init(2, priKey);
            String outStr = new String(cipher.doFinal(inputByte));
            return outStr;
        } catch (Exception var7) {
            throw new RuntimeException(var7);
        }
    }

    public static String encryptWithRSA2PrivateKey(String privateKey, String plainData) {
        try {
            PKCS8EncodedKeySpec priv_spec = new PKCS8EncodedKeySpec(Base64.getDecoder().decode(privateKey));
            KeyFactory mykeyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
            PrivateKey privKey = mykeyFactory.generatePrivate(priv_spec);
            Cipher cipher = Cipher.getInstance(mykeyFactory.getAlgorithm());
            cipher.init(1, privKey);
            byte[] b = plainData.getBytes(UTF8);
            byte[] encrypt = cipher.doFinal(b);
            return Base64.getEncoder().encodeToString(encrypt);
        } catch (Exception var8) {
            throw new RuntimeException(var8);
        }
    }

    public static String decryptWithRSA2PublicKey(String publicKey, String encryptData) {
        try {
            KeyFactory mykeyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
            X509EncodedKeySpec pub_spec = new X509EncodedKeySpec(Base64.getDecoder().decode(publicKey));
            PublicKey pubKey = mykeyFactory.generatePublic(pub_spec);
            Cipher cipher = Cipher.getInstance(mykeyFactory.getAlgorithm());
            cipher.init(2, pubKey);
            byte[] decrypt = cipher.doFinal(Base64.getDecoder().decode(encryptData));
            return new String(decrypt, UTF8);
        } catch (Exception var7) {
            throw new RuntimeException(var7);
        }
    }

    public static String md5Encrypt(String inputText) {
        if (StringUtils.isBlank(inputText)) {
            throw new IllegalArgumentException("Please enter inputText!");
        }
        try {
            MessageDigest digest = MessageDigest.getInstance("MD5");
            digest.update(inputText.getBytes("UTF8"));
            byte encryptBytes[] = digest.digest();
            int i;
            StringBuffer sb = new StringBuffer();
            for (int offset = 0; offset < encryptBytes.length; offset++) {
                i = encryptBytes[offset];
                if (i < 0)
                    i += 256;
                if (i < 16)
                    sb.append("0");
                sb.append(Integer.toHexString(i));
            }
            return sb.toString();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
