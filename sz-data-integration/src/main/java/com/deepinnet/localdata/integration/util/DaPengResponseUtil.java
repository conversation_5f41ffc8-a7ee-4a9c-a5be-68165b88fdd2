package com.deepinnet.localdata.integration.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;

/**
 * Description: 大鹏响应处理工具类
 * 专门处理大鹏接口return字段为JSON字符串的特殊格式
 * Date: 2025/9/8
 * Author: qoder
 */
@Slf4j
public class DaPengResponseUtil {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 转换大鹏接口的特殊响应格式
     * 处理return字段为JSON字符串的情况
     *
     * @param originalResponse 原始响应JSON字符串
     * @return 转换后的标准格式JSON字符串
     */
    public static String convertDaPengResponse(String originalResponse) {
        if (originalResponse == null || originalResponse.trim().isEmpty()) {
            log.warn("原始响应为空");
            return originalResponse;
        }

        try {
            log.debug("开始转换大鹏响应格式，原始响应: {}", originalResponse);
            
            // 首先解析外层JSON
            JsonNode rootNode = objectMapper.readTree(originalResponse);
            
            if (rootNode == null) {
                log.warn("解析响应JSON失败，响应体为空或格式错误");
                return originalResponse;
            }

            // 检查是否有getDataJsonResponse字段
            JsonNode getDataJsonResponseNode = rootNode.get("getDataJsonResponse");
            if (getDataJsonResponseNode == null) {
                log.warn("响应中缺少getDataJsonResponse字段，可能不是大鹏接口响应");
                return originalResponse;
            }

            // 获取return字段（这是一个JSON字符串）
            JsonNode returnNode = getDataJsonResponseNode.get("return");
            if (returnNode == null) {
                log.warn("响应中缺少return字段");
                return originalResponse;
            }

            String returnJsonStr = returnNode.asText();
            log.debug("提取到的return字段内容: {}", returnJsonStr);
            
            // 解析return字段的JSON字符串
            JsonNode returnDataNode = objectMapper.readTree(returnJsonStr);
            if (returnDataNode == null) {
                log.warn("解析return字段JSON失败: {}", returnJsonStr);
                return originalResponse;
            }

            // 重新构建完整的响应结构
            ObjectNode newResponse = objectMapper.createObjectNode();
            ObjectNode newGetDataJsonResponse = objectMapper.createObjectNode();
            
            // 将解析后的return数据放回
            newGetDataJsonResponse.put("return", returnJsonStr); // 保持原始字符串
            
            // 提取data部分
            JsonNode dataNode = returnDataNode.get("data");
            if (dataNode != null) {
                newGetDataJsonResponse.set("data", dataNode);
            } else {
                log.warn("return字段中缺少data部分");
                // 如果没有data字段，可能整个return就是data
                newGetDataJsonResponse.set("data", returnDataNode);
            }
            
            newResponse.set("getDataJsonResponse", newGetDataJsonResponse);

            // 转换为字符串
            String newResponseStr = objectMapper.writeValueAsString(newResponse);
            log.debug("重构后的响应: {}", newResponseStr);
            
            return newResponseStr;

        } catch (Exception e) {
            log.error("转换大鹏响应格式失败，返回原始响应", e);
            return originalResponse;
        }
    }

    /**
     * 检查响应是否为大鹏接口格式
     *
     * @param response 响应JSON字符串
     * @return true表示是大鹏接口格式，false表示不是
     */
    public static boolean isDaPengResponse(String response) {
        if (response == null || response.trim().isEmpty()) {
            return false;
        }

        try {
            JsonNode rootNode = objectMapper.readTree(response);
            if (rootNode == null) {
                return false;
            }

            // 检查是否有getDataJsonResponse字段
            JsonNode getDataJsonResponseNode = rootNode.get("getDataJsonResponse");
            if (getDataJsonResponseNode == null) {
                return false;
            }

            // 检查是否有return字段
            JsonNode returnNode = getDataJsonResponseNode.get("return");
            return returnNode != null;

        } catch (Exception e) {
            log.debug("检查大鹏响应格式时发生异常", e);
            return false;
        }
    }

    /**
     * 提取return字段的JSON内容
     *
     * @param response 大鹏接口响应
     * @return return字段的JSON字符串，如果提取失败返回null
     */
    public static String extractReturnJson(String response) {
        if (!isDaPengResponse(response)) {
            return null;
        }

        try {
            JsonNode rootNode = objectMapper.readTree(response);
            JsonNode getDataJsonResponseNode = rootNode.get("getDataJsonResponse");
            JsonNode returnNode = getDataJsonResponseNode.get("return");
            return returnNode.asText();
        } catch (Exception e) {
            log.error("提取return字段失败", e);
            return null;
        }
    }

    /**
     * 验证转换后的响应格式是否正确
     *
     * @param convertedResponse 转换后的响应
     * @return true表示格式正确，false表示格式错误
     */
    public static boolean validateConvertedResponse(String convertedResponse) {
        if (convertedResponse == null || convertedResponse.trim().isEmpty()) {
            return false;
        }

        try {
            JsonNode rootNode = objectMapper.readTree(convertedResponse);
            if (rootNode == null) {
                return false;
            }

            JsonNode getDataJsonResponseNode = rootNode.get("getDataJsonResponse");
            if (getDataJsonResponseNode == null) {
                return false;
            }

            JsonNode returnNode = getDataJsonResponseNode.get("return");
            JsonNode dataNode = getDataJsonResponseNode.get("data");

            // 必须同时有return和data字段
            return returnNode != null && dataNode != null;

        } catch (Exception e) {
            log.debug("验证转换后响应格式时发生异常", e);
            return false;
        }
    }

    /**
     * 获取响应摘要信息（用于调试）
     *
     * @param response 响应JSON字符串
     * @return 摘要信息
     */
    public static String getResponseSummary(String response) {
        if (response == null || response.trim().isEmpty()) {
            return "响应为空";
        }

        try {
            boolean isDaPeng = isDaPengResponse(response);
            if (!isDaPeng) {
                return "非大鹏接口响应格式";
            }

            String returnJson = extractReturnJson(response);
            if (returnJson == null) {
                return "大鹏接口响应，但无法提取return字段";
            }

            JsonNode returnDataNode = objectMapper.readTree(returnJson);
            JsonNode dataNode = returnDataNode.get("data");
            
            if (dataNode != null && dataNode.has("totalRecords")) {
                int totalRecords = dataNode.get("totalRecords").asInt();
                return String.format("大鹏接口响应，总记录数: %d", totalRecords);
            } else {
                return "大鹏接口响应，格式正常";
            }

        } catch (Exception e) {
            return "响应格式解析异常: " + e.getMessage();
        }
    }
}
