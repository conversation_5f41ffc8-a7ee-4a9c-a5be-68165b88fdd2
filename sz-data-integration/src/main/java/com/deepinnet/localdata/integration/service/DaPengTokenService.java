package com.deepinnet.localdata.integration.service;

import com.deepinnet.localdata.integration.constants.ActionConstants;
import com.deepinnet.localdata.integration.impl.DaPengTokenClientImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 大鹏Token管理服务
 * 负责Token的动态替换和管理
 * Date: 2025/9/1
 * Author: qoder
 */
@Service
@Slf4j
public class DaPengTokenService {

    @Resource
    private DaPengTokenClientImpl tokenClient;

    /**
     * Token正则表达式匹配模式
     * 匹配格式：/services/DB_NAME/module/TOKEN_VALUE/getDataJson
     */
    private static final Pattern TOKEN_PATTERN = Pattern.compile("(/services/[^/]+/[^/]+/)([a-f0-9_]+)(/getDataJson)");

    /**
     * Token占位符正则表达式匹配模式
     * 匹配格式：/services/DB_NAME/module/{DYNAMIC_TOKEN}/getDataJson
     */
    private static final Pattern TOKEN_PLACEHOLDER_PATTERN = Pattern
            .compile("(/services/[^/]+/[^/]+/)\\{DYNAMIC_TOKEN\\}(/getDataJson)");

    /**
     * 将接口URL中的硬编码Token替换为动态Token
     * 增强版本：支持重试和降级机制
     *
     * @param originalUrl 原始URL（包含硬编码Token）
     * @return 替换后的URL（包含动态Token）
     */
    public String replaceTokenInUrl(String originalUrl) {
        return replaceTokenInUrl(originalUrl, true);
    }

    /**
     * 将接口URL中的硬编码Token替换为动态Token（内部方法）
     *
     * @param originalUrl 原始URL（包含硬编码Token）
     * @param enableRetry 是否启用重试机制
     * @return 替换后的URL（包含动态Token）
     */
    public String replaceTokenInUrl(String originalUrl, boolean enableRetry) {
        log.info("开始替换Token，原始URL: {}", originalUrl);
        int maxAttempts = enableRetry ? 3 : 1;
        log.debug("最大尝试次数: {}", maxAttempts);

        for (int attempt = 1; attempt <= maxAttempts; attempt++) {
            try {
                log.info("第{}\u6b21尝试获取Token...", attempt);
                // 获取当前有效的Token
                String currentToken = tokenClient.getCurrentValidToken();
                log.info("获取到的Token: {}",
                        currentToken != null ? currentToken.substring(0, Math.min(20, currentToken.length())) + "..."
                                : "null");

                if (!StringUtils.hasText(currentToken)) {
                    if (attempt < maxAttempts) {
                        log.warn("第{}次尝试获取Token失败，将重试...", attempt);
                        try {
                            Thread.sleep(1000 * attempt); // 递增延迟
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                            break;
                        }
                        continue;
                    } else {
                        log.error("无法获取有效的动态Token，使用原始URL: {}", originalUrl);
                        return originalUrl;
                    }
                }

                // 优先检查是否包含占位符
                log.debug("检查URL是否包含占位符: {}", ActionConstants.DA_PENG_TOKEN_PLACEHOLDER);
                if (originalUrl.contains(ActionConstants.DA_PENG_TOKEN_PLACEHOLDER)) {
                    log.info("发现占位符，开始替换...");
                    String newUrl = originalUrl.replace(ActionConstants.DA_PENG_TOKEN_PLACEHOLDER, currentToken);
                    if (attempt > 1) {
                        log.info("Token占位符替换成功（第{}次尝试）: {} -> {}", attempt, originalUrl, newUrl);
                    } else {
                        log.info("Token占位符替换成功: {} -> {}", originalUrl, newUrl);
                    }
                    return newUrl;
                } else {
                    log.debug("未找到占位符，尝试正则表达式替换...");
                }

                // 使用正则表达式替换旧Token（保持向后兼容）
                Matcher matcher = TOKEN_PATTERN.matcher(originalUrl);
                if (matcher.find()) {
                    String newUrl = matcher.replaceFirst("$1" + currentToken + "$3");
                    if (attempt > 1) {
                        log.info("Token替换成功（第{}次尝试）: {} -> {}", attempt, originalUrl, newUrl);
                    } else {
                        log.debug("Token替换成功: {} -> {}", originalUrl, newUrl);
                    }
                    return newUrl;
                }

                log.warn("URL格式不匹配Token替换模式: {}", originalUrl);
                return originalUrl;

            } catch (Exception e) {
                if (attempt < maxAttempts) {
                    log.warn("第{}次Token替换尝试异常，将重试: {}", attempt, e.getMessage());
                    try {
                        Thread.sleep(1000 * attempt);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                } else {
                    log.error("Token替换过程中发生异常，使用原始URL: {}", originalUrl, e);
                    return originalUrl;
                }
            }
        }

        log.error("Token替换失败，已经尝试{}次，使用原始URL: {}", maxAttempts, originalUrl);
        return originalUrl;
    }

    /**
     * 批量替换多个URL中的Token
     *
     * @param urls URL数组
     * @return 替换后的URL数组
     */
    public String[] replaceTokenInUrls(String... urls) {
        String[] result = new String[urls.length];
        for (int i = 0; i < urls.length; i++) {
            result[i] = replaceTokenInUrl(urls[i]);
        }
        return result;
    }

    /**
     * 验证Token是否需要刷新
     *
     * @return true如果Token需要刷新，false如果Token仍然有效
     */
    public boolean needRefreshToken() {
        String currentToken = tokenClient.getCurrentValidToken();
        return !StringUtils.hasText(currentToken);
    }

    /**
     * 手动刷新Token
     *
     * @return 刷新是否成功
     */
    public boolean refreshToken() {
        try {
            tokenClient.clearTokenCache();
            String newToken = tokenClient.getCurrentValidToken();
            boolean success = StringUtils.hasText(newToken);

            if (success) {
                log.info("Token刷新成功");
            } else {
                log.error("Token刷新失败");
            }

            return success;
        } catch (Exception e) {
            log.error("Token刷新过程中发生异常", e);
            return false;
        }
    }

    /**
     * 从URL中提取当前使用的Token
     *
     * @param url 包含Token的URL
     * @return 提取的Token值，如果无法提取则返回null
     */
    public String extractTokenFromUrl(String url) {
        Matcher matcher = TOKEN_PATTERN.matcher(url);
        if (matcher.find()) {
            return matcher.group(2); // 第二个捕获组是Token
        }
        return null;
    }

    /**
     * 检查URL是否包含有效的Token格式
     *
     * @param url 要检查的URL
     * @return true如果URL包含Token格式，false否则
     */
    public boolean hasTokenPattern(String url) {
        // 检查是否包含占位符
        if (url.contains(ActionConstants.DA_PENG_TOKEN_PLACEHOLDER)) {
            return true;
        }
        // 检查是否匹配旧的Token模式（向后兼容）
        return TOKEN_PATTERN.matcher(url).find();
    }
}