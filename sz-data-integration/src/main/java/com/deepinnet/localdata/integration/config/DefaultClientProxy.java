package com.deepinnet.localdata.integration.config;

import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;

/**
 * Description:
 * Date: 2024/12/24
 * Author: lijunheng
 */
public class DefaultClientProxy implements InvocationHandler {

    @Override
    public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
        throw new UnsupportedOperationException("Operation not supported");
    }

    public static <T> T createDefaultClient(Class<T> clientInterface) {
        return (T) Proxy.newProxyInstance(clientInterface.getClassLoader(), new Class[]{clientInterface}, new DefaultClientProxy());
    }
}
