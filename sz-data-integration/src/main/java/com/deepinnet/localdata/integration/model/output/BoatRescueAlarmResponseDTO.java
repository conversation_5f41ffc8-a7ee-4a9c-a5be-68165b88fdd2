package com.deepinnet.localdata.integration.model.output;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Description: 船舶求助报警信息响应DTO
 * Date: 2025/8/27
 * Author: qoder
 */
@Data
public class BoatRescueAlarmResponseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 获取数据Json响应
     */
    @JsonProperty("getDataJsonResponse")
    private GetDataJsonResponse getDataJsonResponse;

    @Data
    public static class GetDataJsonResponse implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 返回状态信息（注意：这里是字符串，不是对象）
         */
        @JsonProperty("return")
        private String returnInfo;

        /**
         * 数据内容
         */
        private DataInfo data;
    }

    @Data
    public static class ReturnInfo implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 状态码：1：成功，-1：失败，401：token有误
         */
        private String code;

        /**
         * 状态信息
         */
        private String message;

        /**
         * 是否支持搜索
         */
        @JsonProperty("hasSearch")
        private Boolean hasSearch;

        /**
         * 真实数据类型
         */
        @JsonProperty("realDataType")
        private Integer realDataType;

        /**
         * 错误标识
         */
        @JsonProperty("errorFlag")
        private Integer errorFlag;

        /**
         * 是否为文件
         */
        @JsonProperty("isFile")
        private Boolean isFile;
    }

    @Data
    public static class DataInfo implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 总记录数
         */
        private Integer totalRecords;

        /**
         * 错误标识，0表示正常，1表示错误
         */
        private Integer errorFlag;

        /**
         * 数据结果列表
         */
        private List<AlarmRecord> result;
    }

    @Data
    public static class AlarmRecord implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 报警ID
         */
        private String alarmId;

        /**
         * 船舶名称
         */
        private String boatName;

        /**
         * 船舶编号
         */
        private String boatNumber;

        /**
         * 船舶类型
         */
        private String boatType;

        /**
         * 报警时间
         */
        private String alarmTime;

        /**
         * 报警类型
         */
        private String alarmType;

        /**
         * 报警级别
         */
        private String alarmLevel;

        /**
         * 报警位置经度
         */
        private String longitude;

        /**
         * 报警位置纬度
         */
        private String latitude;

        /**
         * 报警描述
         */
        private String alarmDescription;

        /**
         * 联系人
         */
        private String contactPerson;

        /**
         * 联系电话
         */
        private String contactPhone;

        /**
         * 处理状态
         */
        private String status;

        /**
         * 处理时间
         */
        private String processTime;

        /**
         * 处理人员
         */
        private String processor;

        /**
         * 处理结果
         */
        private String processResult;

        /**
         * 创建时间
         */
        private String createTime;

        /**
         * 更新时间
         */
        private String updateTime;

        /**
         * 备注
         */
        private String remark;
    }
}