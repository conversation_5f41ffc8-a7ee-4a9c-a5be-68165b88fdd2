package com.deepinnet.localdata.integration.http;

import cn.hutool.json.JSONUtil;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.util.JsonUtil;
import com.deepinnet.localdata.integration.error.BizErrorCode;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MultiValuedMap;
import org.apache.commons.collections4.multimap.ArrayListValuedHashMap;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.*;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.*;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.X509HostnameVerifier;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultHttpRequestRetryHandler;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicHeader;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.ssl.SSLContexts;
import org.apache.http.ssl.TrustStrategy;
import org.apache.http.util.EntityUtils;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLException;
import javax.net.ssl.SSLSession;
import javax.net.ssl.SSLSocket;
import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.security.KeyStore;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.*;

@Slf4j
public class HttpUtils {

    private static HttpClient httpClient = getHttpClient();

    /**
     * get请求 - 支持多值参数
     *
     * @param url
     * @param requestParamMap
     * @param headerMap
     * @return
     */
    public static HttpResult doGet(String url, MultiValuedMap<String, String> requestParamMap,
                                   MultiValuedMap<String, String> headerMap) {
        log.debug("doGet url: {}, params: {}, header: {}", url, requestParamMap, headerMap);
        HttpGet httpGet = new HttpGet(createUrlWithMultiValuedMap(url, requestParamMap));
        Header[] headers = convertMultiValuedHeaders(headerMap);
        if (headers.length > 0) {
            httpGet.setHeaders(headers);
        }
        return execute(httpGet);
    }

    public static HttpResult doGet(String url, MultiValuedMap<String, String> requestParamMap, String requestBody,
                                   MultiValuedMap<String, String> headerMap) {
        log.debug("doGet url: {}, params: {},requestBody:{}, header: {}", url, requestParamMap, requestBody, headerMap);
        try {
            BaseHttpMethod.HttpGetWithEntity oHttpGet = new BaseHttpMethod.HttpGetWithEntity(
                    createUrlWithMultiValuedMap(url, requestParamMap).toURL().toString());
            HttpEntity httpEntity = new StringEntity(requestBody, ContentType.APPLICATION_JSON);
            oHttpGet.setEntity(httpEntity);

            Header[] headers = convertMultiValuedHeaders(headerMap);
            if (headers.length > 0) {
                oHttpGet.setHeaders(headers);
            }
            return execute(oHttpGet);
        } catch (MalformedURLException e) {
            log.error("请求的URL格式异常，url: [{}]，请求参数:[{}]", url, JSONUtil.toJsonStr(requestParamMap), e);
            throw new BizException(BizErrorCode.NETWORK_REQUEST_ERROR.getCode(),
                    BizErrorCode.NETWORK_REQUEST_ERROR.getDesc());
        }
    }

    /**
     * post请求 - 支持多值参数和指定ContentType
     *
     * @param url
     * @param requestParamMap
     * @param headerMap
     * @param requestBody
     * @param contentType
     * @return
     */
    @SneakyThrows
    public static HttpResult doPost(String url, MultiValuedMap<String, String> requestParamMap,
                                    MultiValuedMap<String, String> headerMap, String requestBody, ContentType contentType) {
        log.debug("doPost url: {}, params: {},requestBody:{}, header: {}", url, requestParamMap, requestBody,
                headerMap);
        if (headerMap == null) {
            headerMap = new ArrayListValuedHashMap<>();
        }
        headerMap.put("Content-Type", contentType.toString());
        HttpPost httpPost = new HttpPost(createUrlWithMultiValuedMap(url, requestParamMap));
        if (StringUtils.isNotBlank(requestBody)) {
            if (contentType.equals(ContentType.APPLICATION_JSON)) {
                StringEntity stringEntity = new StringEntity(requestBody, contentType);
                httpPost.setEntity(stringEntity);
            }
            if (contentType.equals(ContentType.APPLICATION_FORM_URLENCODED)) {
                // 构造请求体
                Map<String, String> requestBodyMap = JsonUtil.parseJson(requestBody, new TypeReference<>() {
                });
                List<NameValuePair> params = new ArrayList<>();
                for (Map.Entry<String, String> param : requestBodyMap.entrySet()) {
                    params.add(new BasicNameValuePair(param.getKey(), param.getValue()));
                }
                httpPost.setEntity(new UrlEncodedFormEntity(params, StandardCharsets.UTF_8));
            }
        }
        Header[] headers = convertMultiValuedHeaders(headerMap);
        if (headers.length > 0) {
            httpPost.setHeaders(headers);
        }
        return execute(httpPost);
    }

    public static HttpResult doPut(String url, MultiValuedMap<String, String> headerMap, String requestBody) {
        log.debug("doPut url: {}, requestBody:{}, header: {}", url, requestBody, headerMap);
        HttpPut httpPut = new HttpPut(createUrlWithMultiValuedMap(url, null));
        httpPut.setEntity(new StringEntity(requestBody, ContentType.APPLICATION_JSON));
        Header[] headers = convertMultiValuedHeaders(headerMap);
        if (headers.length > 0) {
            httpPut.setHeaders(headers);
        }
        return execute(httpPut);
    }

    public static HttpResult doDelete(String url, MultiValuedMap<String, String> headerMap,
                                      MultiValuedMap<String, String> requestParamMap) {
        log.debug("doDelete url: {}, params: {}, header: {}", url, requestParamMap, headerMap);
        HttpDelete httpDelete = new HttpDelete(createUrlWithMultiValuedMap(url, requestParamMap));
        Header[] headers = convertMultiValuedHeaders(headerMap);
        if (headers.length > 0) {
            httpDelete.setHeaders(headers);
        }
        return execute(httpDelete);
    }

    private static URI createUrlWithMultiValuedMap(String url, MultiValuedMap<String, String> requestParamMap) {
        try {
            List<NameValuePair> nvps = turnMultiValuedMapToNameValuePair(requestParamMap);
            URIBuilder uriBuilder = new URIBuilder(url);
            uriBuilder.addParameters(nvps);
            return uriBuilder.build();
        } catch (URISyntaxException e) {
            log.error("请求的URL异常，url: [{}]，请求参数:[{}]", url, requestParamMap, e);
            throw new BizException(BizErrorCode.NETWORK_REQUEST_ERROR.getCode(),
                    BizErrorCode.NETWORK_REQUEST_ERROR.getDesc());
        }
    }

    private static List<NameValuePair> turnMultiValuedMapToNameValuePair(MultiValuedMap<String, String> params) {
        List<NameValuePair> list = new ArrayList<>();
        if (params == null) {
            return list;
        }
        for (String key : params.keySet()) {
            Collection<String> values = params.get(key);
            for (String value : values) {
                list.add(new BasicNameValuePair(key, value));
            }
        }
        return list;
    }

    /**
     * http 处理
     *
     * @param httpRequestBase httpRequestBase
     * @return 响应数据
     * @throws IOException 异常捕获
     */
    private static HttpResult execute(HttpRequestBase httpRequestBase) {
        HttpResponse response = null;
        try {
            response = httpClient.execute(httpRequestBase);
            HttpResult httpResult = new HttpResult();
            // 检查状态码
            int statusCode = response.getStatusLine().getStatusCode();
            httpResult.setStatusCode(statusCode);
            Optional.ofNullable(response.getAllHeaders()).stream().flatMap(Arrays::stream)
                    .forEach(
                            header -> httpResult.getHeader().put(header.getName(), header.getValue()));
            if (response.getEntity() != null) {
                String responseInfo = EntityUtils.toString(response.getEntity(), Consts.UTF_8);
                log.debug("HTTP request URI: [{}], Status Code: {}, Response: {}", httpRequestBase.getURI(), statusCode,
                        responseInfo);
                httpResult.setBody(responseInfo);
            }
            return httpResult;
        } catch (Exception e) {
            httpRequestBase.releaseConnection();
            httpRequestBase.abort();
            log.error("网络异常, url: [{}],response:[{}]", httpRequestBase.getURI().toString(), JSONUtil.toJsonStr(response), e);
            throw new BizException(BizErrorCode.NETWORK_REQUEST_ERROR.getCode(),
                    BizErrorCode.NETWORK_REQUEST_ERROR.getDesc(), e);
        } finally {
            // 确保完全消耗 Entity，连接被正确回收
            if (response != null && response.getEntity() != null) {
                try {
                    EntityUtils.consume(response.getEntity());
                } catch (IOException e) {
                    log.warn("Failed to consume HTTP entity, URI: [{}]", httpRequestBase.getURI(), e);
                }
            }
        }
    }

    private static Header[] convertMultiValuedHeaders(MultiValuedMap<String, String> headerMap) {
        if (headerMap == null || headerMap.isEmpty()) {
            return new Header[0];
        }
        List<Header> headers = new ArrayList<>();
        for (String key : headerMap.keySet()) {
            Collection<String> values = headerMap.get(key);
            for (String value : values) {
                headers.add(new BasicHeader(key, value));
            }
        }
        return headers.toArray(new Header[0]);
    }

    private static HttpClient getHttpClient() {
        try {
            KeyStore trustStore = KeyStore.getInstance(KeyStore.getDefaultType());
            // 设置信任签名
            SSLContext sslcontext = SSLContexts.custom().loadTrustMaterial(trustStore, new TrustStrategy() {
                @Override
                public boolean isTrusted(X509Certificate[] x509Certificates, String s) throws CertificateException {
                    return true;
                }
            }).build();
            X509HostnameVerifier hostnameVerifier = new X509HostnameVerifier() {
                @Override
                public void verify(String s, SSLSocket sslSocket) throws IOException {

                }

                @Override
                public void verify(String s, X509Certificate x509Certificate) throws SSLException {

                }

                @Override
                public void verify(String s, String[] strings, String[] strings1) throws SSLException {

                }

                @Override
                public boolean verify(String s, SSLSession sslSession) {
                    return true;
                }
            };
            // 设置协议http和https对应的处理socket链接工厂的对象
            Registry<ConnectionSocketFactory> socketFactoryRegistry = RegistryBuilder.<ConnectionSocketFactory>create()
                    .register("http", PlainConnectionSocketFactory.INSTANCE)
                    .register("https", new SSLConnectionSocketFactory(sslcontext, hostnameVerifier))
                    .build();

            PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager(
                    socketFactoryRegistry);

            // 创建 RequestConfig 配置超时时间
            RequestConfig requestConfig = RequestConfig.custom()
                    .setConnectTimeout(5000) // 设置连接超时时间为 5000 毫秒
                    .setConnectionRequestTimeout(3000) // 设置从连接池获取连接的超时时间为 3000 毫秒
                    .setSocketTimeout(30000) // 增加读取数据的超时时间为 30000 毫秒（原来为10000毫秒）
                    .build();

            return HttpClientBuilder.create()
                    .setRetryHandler(new DefaultHttpRequestRetryHandler(0, false))
                    .setConnectionManager(connectionManager)
                    .setDefaultRequestConfig(requestConfig)
                    .setRedirectStrategy(new RestRedirectStrategy()).build();
        } catch (Exception e) {
            log.error("获取httpClient异常", e);
            throw new BizException(BizErrorCode.NETWORK_REQUEST_ERROR.getCode(),
                    BizErrorCode.NETWORK_REQUEST_ERROR.getDesc());
        }

    }
}
