package com.deepinnet.localdata.integration.model.output;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Description: 通用数据响应DTO基类
 * 适用于大鹏接口的标准响应格式
 * Date: 2025/9/8
 * Author: qoder
 */
@Data
public class BaseDataResponseDTO<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 获取数据Json响应
     */
    @JsonProperty("getDataJsonResponse")
    private GetDataJsonResponse<T> getDataJsonResponse;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class GetDataJsonResponse<T> implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 返回状态信息（注意：这里是字符串，不是对象）
         */
        @JsonProperty("return")
        private String returnInfo;

        /**
         * 数据内容
         */
        private DataInfo<T> data;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class DataInfo<T> implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 总记录数
         */
        private Integer totalRecords;

        /**
         * 页号
         */
        private Integer pageNo;

        /**
         * 页面大小
         */
        private Integer pageSize;

        /**
         * 错误标识，0表示正常，1表示错误
         */
        private Integer errorFlag;

        /**
         * 是否支持分页
         */
        private Boolean isPageable;

        /**
         * 数据结果列表
         */
        private List<T> result;

        // title字段被忽略，不进行反序列化
        // 因为title字段意义不大，且结构复杂
    }

    /**
     * 返回状态信息解析类（用于解析returnInfo字符串）
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ReturnInfo implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 状态码：1：成功，-1：失败，401：token有误
         */
        private String code;

        /**
         * 状态信息
         */
        private String message;

        /**
         * 是否支持搜索
         */
        @JsonProperty("hasSearch")
        private Boolean hasSearch;

        /**
         * 真实数据类型
         */
        @JsonProperty("realDataType")
        private Integer realDataType;

        /**
         * 错误标识
         */
        @JsonProperty("errorFlag")
        private Integer errorFlag;

        /**
         * 是否为文件
         */
        @JsonProperty("isFile")
        private Boolean isFile;
    }

    /**
     * 判断请求是否成功
     * 通过解析returnInfo字符串中的code字段判断
     */
    public boolean isSuccess() {
        if (getDataJsonResponse() == null || getDataJsonResponse().getReturnInfo() == null) {
            return false;
        }
        
        String returnInfo = getDataJsonResponse().getReturnInfo();
        // 简单的字符串匹配判断成功状态
        return returnInfo.contains("\"code\":1") || returnInfo.contains("\"code\":\"1\"");
    }

    /**
     * 获取数据列表
     */
    public List<T> getResultList() {
        if (getDataJsonResponse() == null || getDataJsonResponse().getData() == null) {
            return null;
        }
        return getDataJsonResponse().getData().getResult();
    }

    /**
     * 获取总记录数
     */
    public Integer getTotalRecords() {
        if (getDataJsonResponse() == null || getDataJsonResponse().getData() == null) {
            return null;
        }
        return getDataJsonResponse().getData().getTotalRecords();
    }

    /**
     * 获取页号
     */
    public Integer getPageNo() {
        if (getDataJsonResponse() == null || getDataJsonResponse().getData() == null) {
            return null;
        }
        return getDataJsonResponse().getData().getPageNo();
    }

    /**
     * 获取页面大小
     */
    public Integer getPageSize() {
        if (getDataJsonResponse() == null || getDataJsonResponse().getData() == null) {
            return null;
        }
        return getDataJsonResponse().getData().getPageSize();
    }

    /**
     * 判断是否支持分页
     */
    public Boolean getIsPageable() {
        if (getDataJsonResponse() == null || getDataJsonResponse().getData() == null) {
            return null;
        }
        return getDataJsonResponse().getData().getIsPageable();
    }
}
