package com.deepinnet.localdata.integration.model.output;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 事项清单响应DTO
 * 根据接口文档定义的响应结构
 */
@Data
public class EventReportItemsResponseDTO implements Serializable {

    /**
     * 自定义数据
     */
    private Custom custom;

    /**
     * 状态信息
     */
    private Status status;

    @Data
    public static class Custom implements Serializable {
        /**
         * 事项列表
         */
        private List<Item> items;
    }

    @Data
    public static class Item implements Serializable {
        /**
         * 二级事项目录编码
         */
        private String secondcatalog;

        /**
         * 二级事项目录名称
         */
        private String secondcatalogname;

        /**
         * 事项清单名称
         */
        private String itemname;

        /**
         * 一级事项目录编码
         */
        private String firstcatalog;

        /**
         * 事项清单编码
         */
        private String itemcode;

        /**
         * 一级事项目录名称
         */
        private String firstcatalogname;
    }

    @Data
    public static class Status implements Serializable {
        /**
         * 状态码
         */
        private Integer code;

        /**
         * 状态文本
         */
        private String text;
    }
}