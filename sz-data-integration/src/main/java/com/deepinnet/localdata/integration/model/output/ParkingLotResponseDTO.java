package com.deepinnet.localdata.integration.model.output;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Description: 停车场信息响应DTO
 * Date: 2025/8/27
 * Author: qoder
 */
@Data
public class ParkingLotResponseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 获取数据Json响应
     */
    @JsonProperty("getDataJsonResponse")
    private GetDataJsonResponse getDataJsonResponse;

    @Data
    public static class GetDataJsonResponse implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 返回状态信息（注意：这里是字符串，不是对象）
         */
        @JsonProperty("return")
        private String returnInfo;

        /**
         * 数据内容
         */
        private DataInfo data;
    }

    @Data
    public static class ReturnInfo implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 状态码：1：成功，-1：失败，401：token有误
         */
        private String code;

        /**
         * 状态信息
         */
        private String message;

        /**
         * 是否支持搜索
         */
        @JsonProperty("hasSearch")
        private Boolean hasSearch;

        /**
         * 真实数据类型
         */
        @JsonProperty("realDataType")
        private Integer realDataType;

        /**
         * 错误标识
         */
        @JsonProperty("errorFlag")
        private Integer errorFlag;

        /**
         * 是否为文件
         */
        @JsonProperty("isFile")
        private Boolean isFile;
    }

    @Data
    public static class DataInfo implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 总记录数
         */
        private Integer totalRecords;

        /**
         * 错误标识，0表示正常，1表示错误
         */
        private Integer errorFlag;

        /**
         * 数据结果列表
         */
        private List<ParkingLotRecord> result;
    }

    @Data
    public static class ParkingLotRecord implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 停车场ID
         */
        private String parkingLotId;

        /**
         * 停车场名称
         */
        private String parkingLotName;

        /**
         * 停车场编号
         */
        private String parkingLotCode;

        /**
         * 停车场类型
         */
        private String parkingLotType;

        /**
         * 停车场性质
         */
        private String parkingLotNature;

        /**
         * 经营方式
         */
        private String businessMode;

        /**
         * 详细地址
         */
        private String address;

        /**
         * 所属区域
         */
        private String district;

        /**
         * 所属街道
         */
        private String street;

        /**
         * 经度
         */
        private String longitude;

        /**
         * 纬度
         */
        private String latitude;

        /**
         * 联系电话
         */
        private String contactPhone;

        /**
         * 传真号码
         */
        private String faxNumber;

        /**
         * 电子邮箱
         */
        private String email;

        /**
         * 邮政编码
         */
        private String postalCode;

        /**
         * 建设年份
         */
        private String establishedYear;

        /**
         * 投资主体
         */
        private String investor;

        /**
         * 运营单位
         */
        private String operatingCompany;

        /**
         * 管理负责人
         */
        private String manager;

        /**
         * 负责人电话
         */
        private String managerPhone;

        /**
         * 占地面积
         */
        private String landArea;

        /**
         * 建筑面积
         */
        private String buildingArea;

        /**
         * 车位总数
         */
        private String totalParkingSpaces;

        /**
         * 地上车位数
         */
        private String aboveGroundSpaces;

        /**
         * 地下车位数
         */
        private String undergroundSpaces;

        /**
         * 机械车位数
         */
        private String mechanicalSpaces;

        /**
         * 残疾人车位数
         */
        private String disabledSpaces;

        /**
         * 新能源车位数
         */
        private String electricCarSpaces;

        /**
         * 充电桩数量
         */
        private String chargingPileCount;

        /**
         * 营业时间
         */
        private String operatingHours;

        /**
         * 是否24小时营业
         */
        private String is24Hours;

        /**
         * 收费标准
         */
        private String chargingStandard;

        /**
         * 收费方式
         */
        private String chargingMethod;

        /**
         * 免费时长
         */
        private String freeTime;

        /**
         * 停车证办理
         */
        private String parkingPermit;

        /**
         * 月卡价格
         */
        private String monthlyCardPrice;

        /**
         * 年卡价格
         */
        private String annualCardPrice;

        /**
         * 支付方式
         */
        private String paymentMethods;

        /**
         * 是否支持预约
         */
        private String supportsReservation;

        /**
         * 预约系统
         */
        private String reservationSystem;

        /**
         * 智能化水平
         */
        private String intelligenceLevel;

        /**
         * 车牌识别系统
         */
        private String licensePlateRecognition;

        /**
         * 停车引导系统
         */
        private String parkingGuidanceSystem;

        /**
         * 反向寻车系统
         */
        private String carLocationSystem;

        /**
         * 监控设备
         */
        private String monitoringEquipment;

        /**
         * 消防设施
         */
        private String fireFacilities;

        /**
         * 照明设施
         */
        private String lightingFacilities;

        /**
         * 通风设施
         */
        private String ventilationFacilities;

        /**
         * 安全设施
         */
        private String securityFacilities;

        /**
         * 洗车服务
         */
        private String carWashService;

        /**
         * 代客泊车
         */
        private String valetParking;

        /**
         * 其他服务
         */
        private String otherServices;

        /**
         * 交通便利程度
         */
        private String transportationAccess;

        /**
         * 周边环境描述
         */
        private String surroundingEnvironment;

        /**
         * 运营状态
         */
        private String operationStatus;

        /**
         * 使用率
         */
        private String utilizationRate;

        /**
         * 高峰时段使用率
         */
        private String peakUtilizationRate;

        /**
         * 平均停车时长
         */
        private String averageParkingDuration;

        /**
         * 日均车流量
         */
        private String dailyTraffic;

        /**
         * 服务质量等级
         */
        private String serviceGrade;

        /**
         * 用户满意度
         */
        private String userSatisfaction;

        /**
         * 投诉建议
         */
        private String complaints;

        /**
         * 违规记录
         */
        private String violationRecords;

        /**
         * 监管部门
         */
        private String regulatoryAuthority;

        /**
         * 监督电话
         */
        private String supervisionPhone;

        /**
         * 数据来源
         */
        private String dataSource;

        /**
         * 创建时间
         */
        private String createTime;

        /**
         * 更新时间
         */
        private String updateTime;

        /**
         * 备注
         */
        private String remark;
    }
}