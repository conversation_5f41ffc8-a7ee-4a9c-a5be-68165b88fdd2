package com.deepinnet.localdata.integration.impl;

import com.deepinnet.localdata.integration.BoatEquipmentClient;
import com.deepinnet.localdata.integration.constants.ActionConstants;
import com.deepinnet.localdata.integration.http.BaseHttpRequestClient;
import com.deepinnet.localdata.integration.http.HttpMethodEnum;
import com.deepinnet.localdata.integration.http.HttpRequestParamNew;
import com.deepinnet.localdata.integration.model.input.BoatEquipmentQueryDTO;
import com.deepinnet.localdata.integration.model.output.BoatEquipmentResponseDTO;
import com.deepinnet.localdata.integration.impl.DaPengTokenClientImpl;
import org.apache.commons.collections4.MultiValuedMap;
import org.apache.commons.collections4.multimap.ArrayListValuedHashMap;
import org.springframework.stereotype.Component;

/**
 * 船舶设备对应表Client实现
 * Date: 2024/8/29
 * Author: qoder
 */
@Component
public class BoatEquipmentClientImpl extends BaseHttpRequestClient implements BoatEquipmentClient {

    private DaPengTokenClientImpl daPengTokenClient;

    public BoatEquipmentClientImpl(DaPengTokenClientImpl daPengTokenClient) {
        this.daPengTokenClient = daPengTokenClient;
    }

    @Override
    public BoatEquipmentResponseDTO getBoatEquipmentMapping(BoatEquipmentQueryDTO queryDTO) {
        System.out.println("开始调用大鹏接口获取船舶设备对应表数据，参数: " + queryDTO);

        // 直接在这里替换Token占位符
        String action = ActionConstants.BOAT_EQUIPMENT_GET_DATA_JSON;
        if (action.contains(ActionConstants.DA_PENG_TOKEN_PLACEHOLDER)) {
            // 获取当前有效的Token
            String token = daPengTokenClient.getCurrentValidToken();
            if (token != null && !token.isEmpty()) {
                // 替换占位符
                action = action.replace(ActionConstants.DA_PENG_TOKEN_PLACEHOLDER, token);
                System.out.println("Token替换完成: " + ActionConstants.BOAT_EQUIPMENT_GET_DATA_JSON + " -> " + action);
            } else {
                System.out.println("无法获取有效Token，使用原始URL");
            }
        }

        // 构建查询参数
        MultiValuedMap<String, String> paramMap = new ArrayListValuedHashMap<>();
        paramMap.put("pageNo", queryDTO.getPageNo() != null ? queryDTO.getPageNo() : "1");
        paramMap.put("pageSize",
                queryDTO.getPageSize() != null ? queryDTO.getPageSize() : "10");
        if (queryDTO.getSearch() != null) {
            paramMap.put("search", queryDTO.getSearch());
        }

        // 构建HTTP请求参数
        HttpRequestParamNew param = HttpRequestParamNew.builder()
                .method(HttpMethodEnum.GET)
                .action(action)
                .address("http://************:8280") // 数据服务器地址
                .requestParam(paramMap)
                .build();

        BoatEquipmentResponseDTO boatEquipmentResponseDTO = httpRequest(param, BoatEquipmentResponseDTO.class,
                null);
        System.out.println("结束调用大鹏接口获取船舶设备对应表数据");
        return boatEquipmentResponseDTO;
    }

    @Override
    public BoatEquipmentResponseDTO getBoatEquipmentMapping(String pageNo, String pageSize, String search) {
        BoatEquipmentQueryDTO queryDTO = new BoatEquipmentQueryDTO();
        queryDTO.setPageNo(pageNo);
        queryDTO.setPageSize(pageSize);
        queryDTO.setSearch(search);
        return getBoatEquipmentMapping(queryDTO);
    }

    @Override
    public BoatEquipmentResponseDTO getBoatEquipmentMapping() {
        return getBoatEquipmentMapping("1", "10", "");
    }
}