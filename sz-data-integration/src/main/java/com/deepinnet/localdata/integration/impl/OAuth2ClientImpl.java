package com.deepinnet.localdata.integration.impl;

import com.deepinnet.localdata.integration.OAuth2Client;
import com.deepinnet.localdata.integration.model.input.OAuth2TokenRequestDTO;
import com.deepinnet.localdata.integration.model.output.OAuth2TokenResponseDTO;
import org.springframework.stereotype.Component;

import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * OAuth2客户端实现
 */
@Component
public class OAuth2ClientImpl implements OAuth2Client {

    // 模拟客户端数据库
    private static final Map<String, String> CLIENT_DATABASE = new HashMap<>();
    // 模拟token存储
    private static final Map<String, String> TOKEN_STORAGE = new ConcurrentHashMap<>();
    // token过期时间（秒）
    private static final String DEFAULT_EXPIRES_IN = "1800"; // 30分钟

    static {
        // 初始化一些测试客户端
        CLIENT_DATABASE.put("test_client_id", "test_client_secret");
        CLIENT_DATABASE.put("demo_client", "demo_secret_123");
        CLIENT_DATABASE.put("app_client", "app_secret_456");
    }

    @Override
    public OAuth2TokenResponseDTO getToken(OAuth2TokenRequestDTO request) {
        try {
            System.out.println(
                    "OAuth2 token请求: clientId=" + request.getClientId() + ", grantType=" + request.getGrantType());

            // 参数验证
            if (request.getClientId() == null || request.getClientId().trim().isEmpty() ||
                    request.getClientSecret() == null || request.getClientSecret().trim().isEmpty()) {
                System.out.println("客户端ID或密钥为空");
                return OAuth2TokenResponseDTO.error("0");
            }

            if (!"client_credentials".equals(request.getGrantType())) {
                System.out.println("不支持的授权类型: " + request.getGrantType());
                return OAuth2TokenResponseDTO.error("0");
            }

            // 验证客户端凭证
            if (!validateClientCredentials(request.getClientId(), request.getClientSecret())) {
                System.out.println("客户端凭证验证失败: clientId=" + request.getClientId());
                return OAuth2TokenResponseDTO.error("0");
            }

            // 生成access token
            String accessToken = generateAccessToken(request.getClientId());

            // 存储token
            TOKEN_STORAGE.put(accessToken, request.getClientId());

            System.out.println("OAuth2 token生成成功: clientId=" + request.getClientId() + ", accessToken=" + accessToken);

            return OAuth2TokenResponseDTO.success(accessToken, DEFAULT_EXPIRES_IN);

        } catch (Exception e) {
            System.err.println("OAuth2 token获取过程中发生异常: " + e.getMessage());
            return OAuth2TokenResponseDTO.error("0");
        }
    }

    @Override
    public boolean validateClientCredentials(String clientId, String clientSecret) {
        try {
            if (clientId == null || clientId.trim().isEmpty() ||
                    clientSecret == null || clientSecret.trim().isEmpty()) {
                return false;
            }

            String storedSecret = CLIENT_DATABASE.get(clientId);
            return storedSecret != null && storedSecret.equals(clientSecret);

        } catch (Exception e) {
            System.err.println("验证客户端凭证时发生异常: " + e.getMessage());
            return false;
        }
    }

    /**
     * 生成访问令牌
     */
    private String generateAccessToken(String clientId) {
        String payload = clientId + ":" + System.currentTimeMillis() + ":" + UUID.randomUUID().toString();
        return Base64.getEncoder().encodeToString(payload.getBytes()).replace("=", "").replace("+", "-").replace("/",
                "_");
    }

    /**
     * 验证token有效性
     */
    public boolean validateToken(String accessToken) {
        try {
            if (accessToken == null || accessToken.trim().isEmpty()) {
                return false;
            }

            return TOKEN_STORAGE.containsKey(accessToken);
        } catch (Exception e) {
            System.err.println("验证token时发生异常: " + e.getMessage());
            return false;
        }
    }

    /**
     * 获取token对应的客户端ID
     */
    public String getClientIdByToken(String accessToken) {
        return TOKEN_STORAGE.get(accessToken);
    }
}