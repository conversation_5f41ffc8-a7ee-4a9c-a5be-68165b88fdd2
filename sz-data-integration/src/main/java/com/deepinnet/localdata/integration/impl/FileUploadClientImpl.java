package com.deepinnet.localdata.integration.impl;

import com.deepinnet.localdata.integration.FileUploadClient;
import com.deepinnet.localdata.integration.model.output.FileUploadResponseDTO;

import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.UUID;

/**
 * 文件上传客户端实现
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class FileUploadClientImpl implements FileUploadClient {

    private String uploadPath = "./uploads";

    public void setUploadPath(String uploadPath) {
        this.uploadPath = uploadPath;
    }

    @Override
    public FileUploadResponseDTO uploadFile(Object fileObj, String filename, String clienttag, String clientid) {
        try {
            // 验证必要参数
            if (filename == null || filename.trim().isEmpty()) {
                return FileUploadResponseDTO.error("文件名不能为空");
            }

            if (clienttag == null || clienttag.trim().isEmpty()) {
                return FileUploadResponseDTO.error("客户端标识不能为空");
            }

            if (clientid == null || clientid.trim().isEmpty()) {
                return FileUploadResponseDTO.error("应用ID不能为空");
            }

            if (fileObj == null) {
                return FileUploadResponseDTO.error("上传文件不能为空");
            }

            // 创建上传目录
            Path uploadDir = Paths.get(uploadPath);
            if (!Files.exists(uploadDir)) {
                Files.createDirectories(uploadDir);
            }

            // 生成唯一的附件GUID
            String attachGuid = UUID.randomUUID().toString();
            
            // 获取文件扩展名
            String fileExtension = "";
            if (filename.lastIndexOf(".") > 0) {
                fileExtension = filename.substring(filename.lastIndexOf("."));
            }

            // 构建保存的文件名
            String savedFilename = attachGuid + fileExtension;
            Path filePath = uploadDir.resolve(savedFilename);

            // 使用反射处理MultipartFile对象
            try {
                // 获取MultipartFile的getInputStream方法
                InputStream inputStream = (InputStream) fileObj.getClass().getMethod("getInputStream").invoke(fileObj);
                
                // 保存文件
                Files.copy(inputStream, filePath, StandardCopyOption.REPLACE_EXISTING);
                inputStream.close();
                
                // 返回成功响应
                return FileUploadResponseDTO.success(attachGuid);
                
            } catch (Exception e) {
                // 如果反射失败，说明传入的不是MultipartFile对象
                return FileUploadResponseDTO.error("不支持的文件类型");
            }

        } catch (IOException e) {
            return FileUploadResponseDTO.error("文件保存失败: " + e.getMessage());
        } catch (Exception e) {
            return FileUploadResponseDTO.error("文件上传失败: " + e.getMessage());
        }
    }
}