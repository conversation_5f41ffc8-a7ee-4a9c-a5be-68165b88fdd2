package com.deepinnet.localdata.integration.model.output;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Description: 医院信息响应DTO
 * Date: 2025/8/27
 * Author: qoder
 */
@Data
public class HospitalResponseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 获取数据Json响应
     */
    @JsonProperty("getDataJsonResponse")
    private GetDataJsonResponse getDataJsonResponse;

    @Data
    public static class GetDataJsonResponse implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 返回状态信息（注意：这里是字符串，不是对象）
         */
        @JsonProperty("return")
        private String returnInfo;

        /**
         * 数据内容
         */
        private DataInfo data;
    }

    @Data
    public static class ReturnInfo implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 状态码：1：成功，-1：失败，401：token有误
         */
        private String code;

        /**
         * 状态信息
         */
        private String message;

        /**
         * 是否支持搜索
         */
        @JsonProperty("hasSearch")
        private Boolean hasSearch;

        /**
         * 真实数据类型
         */
        @JsonProperty("realDataType")
        private Integer realDataType;

        /**
         * 错误标识
         */
        @JsonProperty("errorFlag")
        private Integer errorFlag;

        /**
         * 是否为文件
         */
        @JsonProperty("isFile")
        private Boolean isFile;
    }

    @Data
    public static class DataInfo implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 总记录数
         */
        private Integer totalRecords;

        /**
         * 错误标识，0表示正常，1表示错误
         */
        private Integer errorFlag;

        /**
         * 数据结果列表
         */
        private List<HospitalRecord> result;
    }

    @Data
    public static class HospitalRecord implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 医院ID
         */
        private String hospitalId;

        /**
         * 医院名称
         */
        private String hospitalName;

        /**
         * 医院编号
         */
        private String hospitalCode;

        /**
         * 医院类型
         */
        private String hospitalType;

        /**
         * 医院等级
         */
        private String hospitalLevel;

        /**
         * 医院分类
         */
        private String hospitalCategory;

        /**
         * 详细地址
         */
        private String address;

        /**
         * 所属区域
         */
        private String district;

        /**
         * 所属街道
         */
        private String street;

        /**
         * 经度
         */
        private String longitude;

        /**
         * 纬度
         */
        private String latitude;

        /**
         * 联系电话
         */
        private String contactPhone;

        /**
         * 急救电话
         */
        private String emergencyPhone;

        /**
         * 传真号码
         */
        private String faxNumber;

        /**
         * 官方网站
         */
        private String officialWebsite;

        /**
         * 电子邮箱
         */
        private String email;

        /**
         * 邮政编码
         */
        private String postalCode;

        /**
         * 建院年份
         */
        private String establishedYear;

        /**
         * 法人代表
         */
        private String legalRepresentative;

        /**
         * 院长姓名
         */
        private String presidentName;

        /**
         * 医院规模
         */
        private String hospitalScale;

        /**
         * 床位数量
         */
        private String bedCount;

        /**
         * 职工人数
         */
        private String staffCount;

        /**
         * 医师人数
         */
        private String doctorCount;

        /**
         * 护士人数
         */
        private String nurseCount;

        /**
         * 占地面积
         */
        private String landArea;

        /**
         * 建筑面积
         */
        private String buildingArea;

        /**
         * 医疗设备总值
         */
        private String medicalEquipmentValue;

        /**
         * 年门诊量
         */
        private String annualOutpatientVolume;

        /**
         * 年住院量
         */
        private String annualInpatientVolume;

        /**
         * 营业时间
         */
        private String operatingHours;

        /**
         * 是否24小时急诊
         */
        private String is24HourEmergency;

        /**
         * 挂号方式
         */
        private String registrationMethod;

        /**
         * 是否支持网上预约
         */
        private String onlineAppointment;

        /**
         * 医保类型
         */
        private String insuranceType;

        /**
         * 是否医保定点
         */
        private String isInsuranceDesignated;

        /**
         * 重点科室
         */
        private String keyDepartments;

        /**
         * 特色服务
         */
        private String specialServices;

        /**
         * 医院荣誉
         */
        private String hospitalHonors;

        /**
         * 认证资质
         */
        private String certifications;

        /**
         * 停车位数量
         */
        private String parkingSpaces;

        /**
         * 是否有停车场
         */
        private String hasParkingLot;

        /**
         * 交通便利程度
         */
        private String transportationAccess;

        /**
         * 周边环境描述
         */
        private String surroundingEnvironment;

        /**
         * 医院介绍
         */
        private String hospitalDescription;

        /**
         * 服务理念
         */
        private String serviceConcept;

        /**
         * 运营状态
         */
        private String operationStatus;

        /**
         * 服务质量等级
         */
        private String serviceGrade;

        /**
         * 患者满意度
         */
        private String patientSatisfaction;

        /**
         * 医疗纠纷情况
         */
        private String medicalDisputes;

        /**
         * 安全事故记录
         */
        private String safetyIncidents;

        /**
         * 监管部门
         */
        private String regulatoryAuthority;

        /**
         * 监督电话
         */
        private String supervisionPhone;

        /**
         * 投诉处理机制
         */
        private String complaintHandling;

        /**
         * 数据来源
         */
        private String dataSource;

        /**
         * 创建时间
         */
        private String createTime;

        /**
         * 更新时间
         */
        private String updateTime;

        /**
         * 备注
         */
        private String remark;
    }
}