package com.deepinnet.localdata.integration.model.output;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Description: 设备报警信息响应DTO
 * Date: 2025/8/26
 * Author: qoder
 */
@Data
public class DeviceAlarmResponseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 获取数据Json响应
     */
    @JsonProperty("getDataJsonResponse")
    private GetDataJsonResponse getDataJsonResponse;

    @Data
    public static class GetDataJsonResponse implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 返回状态信息（注意：这里是字符串，不是对象）
         */
        @JsonProperty("return")
        private String returnInfo;

        /**
         * 数据内容
         */
        private DataInfo data;
    }

    @Data
    public static class ReturnInfo implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 状态码：1：成功，-1：失败，401：token有误
         */
        private String code;

        /**
         * 状态信息
         */
        private String message;

        /**
         * 该服务是否支持搜索：true：支持，false：不支持
         */
        private Boolean hasSearch;

        /**
         * 真实数据类型
         */
        @JsonProperty("realDataType")
        private Integer realDataType;

        /**
         * 错误标识
         */
        @JsonProperty("errorFlag")
        private Integer errorFlag;

        /**
         * 是否为文件
         */
        @JsonProperty("isFile")
        private Boolean isFile;
    }

    @Data
    public static class DataInfo implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 数据总数
         */
        private Integer totalRecords;

        /**
         * 分页页号
         */
        private Integer pageNo;

        /**
         * 单页数量
         */
        private Integer pageSize;

        /**
         * 错误标识：1：服务正常，2：提供方异常，3：平台异常
         */
        private Integer errorFlag;

        /**
         * 是否存在性别字段：true是，false不是
         */
        private Boolean isFile;

        /**
         * 子标题
         */
        private String title;

        /**
         * 结果数据列表
         */
        private List<DeviceAlarmDataItem> result;
    }

    @Data
    public static class DeviceAlarmDataItem implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 字段编号
         */
        private String dataCode;

        /**
         * 字段名称
         */
        private String dataName;

        /**
         * 过滤类型：eq;neq;gt;
         */
        private String filterType;

        /**
         * 字段字典：{"原始值":"字典提示值"}
         */
        private String dicTable;

        /**
         * 共享类型（1：无条件共享，2：有条件共享）
         */
        private String shareKind;
    }
}