package com.deepinnet.localdata.integration;

import com.deepinnet.localdata.integration.model.output.FileUploadResponseDTO;

/**
 * 文件上传客户端接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface FileUploadClient {

    /**
     * 上传文件
     * 
     * @param file      上传的文件
     * @param filename  文件名
     * @param clienttag 客户端标识
     * @param clientid  应用ID
     * @return 文件上传响应
     */
    FileUploadResponseDTO uploadFile(Object file, String filename, String clienttag, String clientid);
}