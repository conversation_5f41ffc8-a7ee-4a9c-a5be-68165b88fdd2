package com.deepinnet.localdata.integration.model.output;

import lombok.Data;

import java.io.Serializable;

/**
 * 大鹏Token响应DTO
 * Date: 2025/9/1
 * Author: qoder
 */
@Data
public class DaPengTokenResponseDTO implements Serializable {

    /**
     * 响应代码
     */
    private String code;

    /**
     * 响应消息
     */
    private String message;

    /**
     * Token数据
     */
    private String data;

    /**
     * 判断响应是否成功
     *
     * @return true如果成功，false如果失败
     */
    public boolean isSuccess() {
        return "20000".equals(code);
    }

    /**
     * 获取Token值
     *
     * @return Token字符串
     */
    public String getToken() {
        return data;
    }
}