package com.deepinnet.localdata.integration.model.input;

import lombok.Data;
import java.util.List;

/**
 * 工单上报请求DTO
 * 根据API文档 2.1.4 工单上报接口规范
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
public class CaseReportRequestDTO {

    /**
     * 工单唯一标识（可选）
     * 字符串，不超过32位
     */
    private String rowguid;

    /**
     * 请求内容（必需）
     * 字符串，不超过2000位
     */
    private String rqstcontent;

    /**
     * 事发地址（可选）
     * 字符串，不超过300位
     */
    private String rqstaddress;

    /**
     * 请求标题（可选）
     * 字符串，不超过150位
     */
    private String rqsttitle;

    /**
     * 联系人联系方式（可选）
     * 字符串，不超过50位
     */
    private String linknumber;

    /**
     * 请求人姓名（可选）
     * 字符串，不超过50位
     */
    private String rqstname;

    /**
     * 详细地址（可选）
     * 字符串，不超过300位
     */
    private String detailaddress;

    /**
     * 事项清单编码（可选）
     */
    private String itemcode;

    /**
     * 街道编码（可选）
     */
    private String streetcode;

    /**
     * 街道名称（可选）
     * 字符串，不超过50位
     */
    private String streetname;

    /**
     * 社区编码（可选）
     */
    private String communitycode;

    /**
     * 社区名称（可选）
     * 字符串，不超过50位
     */
    private String communityname;

    /**
     * 请求时间（可选）
     * 格式：yyyy-MM-dd HH:mm:ss
     */
    private String rqsttime;

    /**
     * 事件来源（可选）
     * 对接时双方约定
     */
    private String rqstsource;

    /**
     * 问题类型（可选）
     * 见2.2.3
     */
    private String msquestiontype;

    /**
     * 办结时限（可选）
     * 格式：yyyy-MM-dd
     */
    private String finishtimelimit;

    /**
     * 坐标x/经度（可选）
     */
    private String gisx;

    /**
     * 坐标y/纬度（可选）
     */
    private String gisy;

    /**
     * 附加数据（可选）
     * 具体见附录2.3附加数据
     * JSON格式
     */
    private String extraparams;

    /**
     * 附件数组（可选）
     */
    private List<FileAttachment> files;

    /**
     * 附件信息
     */
    @Data
    public static class FileAttachment {
        /**
         * 工单附件上传回调 attachguid 值
         */
        private String attachguid;
    }
}