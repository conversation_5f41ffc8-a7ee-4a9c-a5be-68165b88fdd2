package com.deepinnet.localdata.integration;

import com.deepinnet.localdata.integration.model.input.OAuth2TokenRequestDTO;
import com.deepinnet.localdata.integration.model.output.OAuth2TokenResponseDTO;

/**
 * OAuth2客户端接口
 */
public interface OAuth2Client {

    /**
     * 获取OAuth2 access token
     * 
     * @param request OAuth2 token请求
     * @return OAuth2 token响应
     */
    OAuth2TokenResponseDTO getToken(OAuth2TokenRequestDTO request);

    /**
     * 验证客户端凭证
     * 
     * @param clientId     客户端ID
     * @param clientSecret 客户端密钥
     * @return 是否有效
     */
    boolean validateClientCredentials(String clientId, String clientSecret);
}