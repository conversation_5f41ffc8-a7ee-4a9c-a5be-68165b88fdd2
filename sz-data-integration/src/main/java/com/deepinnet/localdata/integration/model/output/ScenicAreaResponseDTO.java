package com.deepinnet.localdata.integration.model.output;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Description: 景区信息响应DTO
 * Date: 2025/8/27
 * Author: qoder
 */
@Data
public class ScenicAreaResponseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 获取数据Json响应
     */
    @JsonProperty("getDataJsonResponse")
    private GetDataJsonResponse getDataJsonResponse;

    @Data
    public static class GetDataJsonResponse implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 返回状态信息（注意：这里是字符串，不是对象）
         */
        @JsonProperty("return")
        private String returnInfo;

        /**
         * 数据内容
         */
        private DataInfo data;
    }

    @Data
    public static class ReturnInfo implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 状态码：1：成功，-1：失败，401：token有误
         */
        private String code;

        /**
         * 状态信息
         */
        private String message;

        /**
         * 是否支持搜索
         */
        @JsonProperty("hasSearch")
        private Boolean hasSearch;

        /**
         * 真实数据类型
         */
        @JsonProperty("realDataType")
        private Integer realDataType;

        /**
         * 错误标识
         */
        @JsonProperty("errorFlag")
        private Integer errorFlag;

        /**
         * 是否为文件
         */
        @JsonProperty("isFile")
        private Boolean isFile;
    }

    @Data
    public static class DataInfo implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 总记录数
         */
        private Integer totalRecords;

        /**
         * 错误标识，0表示正常，1表示错误
         */
        private Integer errorFlag;

        /**
         * 数据结果列表
         */
        private List<ScenicAreaRecord> result;
    }

    @Data
    public static class ScenicAreaRecord implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 景区ID
         */
        private String scenicAreaId;

        /**
         * 景区名称
         */
        private String scenicAreaName;

        /**
         * 景区编号
         */
        private String scenicAreaCode;

        /**
         * 景区类型
         */
        private String scenicAreaType;

        /**
         * 景区等级（如AAAAA级、AAAA级等）
         */
        private String scenicAreaLevel;

        /**
         * 景区分类
         */
        private String scenicAreaCategory;

        /**
         * 所在省份
         */
        private String province;

        /**
         * 所在城市
         */
        private String city;

        /**
         * 所在区县
         */
        private String district;

        /**
         * 所在街道/乡镇
         */
        private String street;

        /**
         * 详细地址
         */
        private String detailedAddress;

        /**
         * 经度
         */
        private String longitude;

        /**
         * 纬度
         */
        private String latitude;

        /**
         * 景区面积（平方公里）
         */
        private String scenicAreaSize;

        /**
         * 开发年份
         */
        private String developmentYear;

        /**
         * 建设投资（万元）
         */
        private String constructionInvestment;

        /**
         * 主要景观类型
         */
        private String mainLandscapeType;

        /**
         * 自然资源特色
         */
        private String naturalResourceFeatures;

        /**
         * 人文资源特色
         */
        private String culturalResourceFeatures;

        /**
         * 核心景点
         */
        private String coreAttractions;

        /**
         * 特色景观
         */
        private String specialLandscapes;

        /**
         * 门票价格（元）
         */
        private String ticketPrice;

        /**
         * 淡季门票价格（元）
         */
        private String offSeasonTicketPrice;

        /**
         * 旺季门票价格（元）
         */
        private String peakSeasonTicketPrice;

        /**
         * 优惠政策
         */
        private String discountPolicy;

        /**
         * 开放时间
         */
        private String openingHours;

        /**
         * 最佳游览季节
         */
        private String bestVisitingSeason;

        /**
         * 建议游览时长（小时）
         */
        private String suggestedVisitDuration;

        /**
         * 年游客量（万人次）
         */
        private String annualVisitorCount;

        /**
         * 日最大承载量（人）
         */
        private String dailyMaxCapacity;

        /**
         * 旅游收入（万元）
         */
        private String tourismRevenue;

        /**
         * 管理机构
         */
        private String managementInstitution;

        /**
         * 法人代表
         */
        private String legalRepresentative;

        /**
         * 景区经理
         */
        private String scenicAreaManager;

        /**
         * 联系电话
         */
        private String contactPhone;

        /**
         * 传真
         */
        private String fax;

        /**
         * 电子邮箱
         */
        private String email;

        /**
         * 官方网站
         */
        private String officialWebsite;

        /**
         * 微信公众号
         */
        private String wechatAccount;

        /**
         * 停车场数量
         */
        private String parkingLotCount;

        /**
         * 停车位数量
         */
        private String parkingSpaces;

        /**
         * 游客中心
         */
        private String visitorCenter;

        /**
         * 导游服务
         */
        private String guideService;

        /**
         * 讲解服务
         */
        private String commentaryService;

        /**
         * 餐饮设施
         */
        private String diningFacilities;

        /**
         * 住宿设施
         */
        private String accommodationFacilities;

        /**
         * 购物设施
         */
        private String shoppingFacilities;

        /**
         * 娱乐设施
         */
        private String entertainmentFacilities;

        /**
         * 医疗设施
         */
        private String medicalFacilities;

        /**
         * 通信设施
         */
        private String communicationFacilities;

        /**
         * 无障碍设施
         */
        private String accessibilityFacilities;

        /**
         * 安全设施
         */
        private String safetyFacilities;

        /**
         * 环保设施
         */
        private String environmentalFacilities;

        /**
         * 智慧旅游设施
         */
        private String smartTourismFacilities;

        /**
         * 交通便利程度
         */
        private String transportationAccessibility;

        /**
         * 公共交通
         */
        private String publicTransportation;

        /**
         * 自驾路线
         */
        private String drivingRoute;

        /**
         * 环境质量等级
         */
        private String environmentalQualityLevel;

        /**
         * 空气质量
         */
        private String airQuality;

        /**
         * 水质状况
         */
        private String waterQuality;

        /**
         * 噪音控制
         */
        private String noiseControl;

        /**
         * 绿化覆盖率（%）
         */
        private String greenCoverageRate;

        /**
         * 生态保护措施
         */
        private String ecologicalProtectionMeasures;

        /**
         * 服务质量等级
         */
        private String serviceQualityLevel;

        /**
         * 游客满意度（%）
         */
        private String visitorSatisfaction;

        /**
         * 服务投诉率（%）
         */
        private String serviceComplaintRate;

        /**
         * 安全事故率
         */
        private String safetyAccidentRate;

        /**
         * 获得荣誉
         */
        private String honors;

        /**
         * 认证资质
         */
        private String certifications;

        /**
         * 获奖情况
         */
        private String awards;

        /**
         * 媒体报道
         */
        private String mediaReports;

        /**
         * 知名度等级
         */
        private String popularityLevel;

        /**
         * 品牌价值（万元）
         */
        private String brandValue;

        /**
         * 发展规划
         */
        private String developmentPlan;

        /**
         * 扩建计划
         */
        private String expansionPlan;

        /**
         * 投资规划（万元）
         */
        private String investmentPlan;

        /**
         * 存在问题
         */
        private String existingProblems;

        /**
         * 改进措施
         */
        private String improvementMeasures;

        /**
         * 发展前景
         */
        private String developmentProspects;

        /**
         * 监管部门
         */
        private String regulatoryDepartment;

        /**
         * 监管措施
         */
        private String regulatoryMeasures;

        /**
         * 检查频次
         */
        private String inspectionFrequency;

        /**
         * 合规状态
         */
        private String complianceStatus;

        /**
         * 备注
         */
        private String remarks;

        /**
         * 数据更新时间
         */
        private String updateTime;

        /**
         * 数据来源
         */
        private String dataSource;

        /**
         * 数据状态
         */
        private String dataStatus;

        /**
         * 创建时间
         */
        private String createTime;

        /**
         * 录入人员
         */
        private String dataEntryPerson;
    }
}