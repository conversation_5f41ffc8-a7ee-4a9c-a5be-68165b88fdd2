package com.deepinnet.localdata.integration.model.output;

import lombok.Data;
import java.util.List;

/**
 * 工单查询响应DTO
 * 根据API文档 2.1.5 工单查询接口规范
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
public class CaseQueryResponseDTO {

    /**
     * 自定义数据
     */
    private Custom custom;

    /**
     * 状态信息
     */
    private Status status;

    @Data
    public static class Custom {
        /**
         * 工单信息
         */
        private CaseInfo caseinfo;
    }

    @Data
    public static class CaseInfo {
        /**
         * 社区编码
         */
        private String communitycode;

        /**
         * 二级事项目录编码
         */
        private String secondcatalogno;

        /**
         * 事项名称
         */
        private String itemname;

        /**
         * 街道名称
         */
        private String streetname;

        /**
         * 一级事项目录编码
         */
        private String firstcatalog;

        /**
         * 社区名称
         */
        private String communityname;

        /**
         * 处理单编号
         */
        private String chdsequid;

        /**
         * 一级事项目录名称
         */
        private String firstcatalogname;

        /**
         * 回复内容
         */
        private String answercontent;

        /**
         * 工单编码
         */
        private String casecode;

        /**
         * 请求时间
         */
        private String rqsttime;

        /**
         * 问题类型
         */
        private String msquestiontype;

        /**
         * 二级事项目录名称
         */
        private String secondcatalogname;

        /**
         * 请求人姓名
         */
        private String rqstname;

        /**
         * 工单GUID
         */
        private String caseguid;

        /**
         * 查询地址
         */
        private String rqstaddress;

        /**
         * 回复内容
         */
        private String answereou;

        /**
         * 事项清单编码
         */
        private String itemcode;

        /**
         * 街道编码
         */
        private String streetcode;

        /**
         * 回复时间
         */
        private String answertime;

        /**
         * 工单状态 (见2.2.4)
         */
        private String casestatus;

        /**
         * 工单标签
         */
        private List<String> caselabels;

        /**
         * 附件数组
         */
        private List<FileAttachment> files;
    }

    @Data
    public static class FileAttachment {
        /**
         * 附件GUID
         */
        private String id;

        /**
         * 附件名称
         */
        private String attachname;

        /**
         * 附件URL
         */
        private String attachurl;
    }

    @Data
    public static class Status {
        /**
         * 状态码
         */
        private String code;

        /**
         * 状态描述
         */
        private String text;
    }

    /**
     * 创建成功响应
     */
    public static CaseQueryResponseDTO success(CaseInfo caseInfo) {
        CaseQueryResponseDTO response = new CaseQueryResponseDTO();

        Custom custom = new Custom();
        custom.setCaseinfo(caseInfo);
        response.setCustom(custom);

        Status status = new Status();
        status.setCode("1");
        status.setText("请求成功");
        response.setStatus(status);

        return response;
    }

    /**
     * 创建失败响应
     */
    public static CaseQueryResponseDTO error(String errorMessage) {
        CaseQueryResponseDTO response = new CaseQueryResponseDTO();

        Custom custom = new Custom();
        custom.setCaseinfo(null);
        response.setCustom(custom);

        Status status = new Status();
        status.setCode("0");
        status.setText(errorMessage != null ? errorMessage : "请求失败");
        response.setStatus(status);

        return response;
    }
}