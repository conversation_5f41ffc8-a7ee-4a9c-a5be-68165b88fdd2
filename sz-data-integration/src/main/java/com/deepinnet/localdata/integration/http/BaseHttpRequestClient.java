package com.deepinnet.localdata.integration.http;

import cn.hutool.core.util.StrUtil;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.util.JsonConvertUtil;
import com.deepinnet.digitaltwin.common.util.JsonUtil;
import com.deepinnet.localdata.integration.error.BizErrorCode;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MultiValuedMap;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ContentType;

import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * Description:
 * Date: 2024/11/11
 * Author: lijunheng
 */
@Slf4j
public class BaseHttpRequestClient {
    public <T> T httpRequest(HttpRequestParam httpRequestParam, Object type) {
        return httpRequest(httpRequestParam, type, ContentType.APPLICATION_JSON);
    }

    public <T> T httpRequest(HttpRequestParam httpRequestParam, Object type, ContentType contentType) {
        HttpRequestParamNew paramNew = HttpRequestParamUtils.convertHttpRequestParam(httpRequestParam);
        return httpRequest(paramNew, type, contentType);
    }

    public <T> T httpRequest(HttpRequestParamNew httpRequestParam, Object type, ContentType contentType) {
        HttpMethodEnum method = httpRequestParam.getMethod();
        MultiValuedMap<String, String> headerMap = httpRequestParam.getRequestHeader();
        String requestBody = HttpRequestParamUtils.getRequestBodyJson(httpRequestParam.getRequestBody());
        MultiValuedMap<String, String> requestParamMap = httpRequestParam.getRequestParam();
        String url = httpRequestParam.getAddress();
        if (StrUtil.isNotBlank(httpRequestParam.getAction())) {
            url = httpRequestParam.getAddress() + httpRequestParam.getAction();
        }
        //解析URL中的占位符
        url = replacePlaceholders(url, requestParamMap);
        HttpResult response;
        switch (method) {
            case GET:
                response = HttpUtils.doGet(url, requestParamMap, headerMap);
                break;
            case POST:
                response = HttpUtils.doPost(url, requestParamMap, headerMap, requestBody, contentType);
                break;
            case PUT:
                response = HttpUtils.doPut(url, headerMap, requestBody);
                break;
            case DELETE:
                response = HttpUtils.doDelete(url, headerMap, requestParamMap);
                break;
            default:
                throw new IllegalArgumentException("not support method: " + method);
        }

        validateResponse(httpRequestParam, response);
        try {
            if (StringUtils.isBlank(response.getBody())) {
                return null;
            }
            return convertResponse(response.getBody(), type);
        } catch (Exception e) {
            log.error("The response result of the request was unexpected, requestUrl:{}, expected return type:{}, response:{}", url, type.getClass(), response, e);
            throw new BizException(BizErrorCode.DATA_INVALID.getCode(), BizErrorCode.DATA_INVALID.getDesc(), e);
        }
    }

    protected <T> T convertResponse(String body, Object type) {
        //这里兼容一下之前的hutool的Json工具
        if (type instanceof cn.hutool.core.lang.TypeReference) {
            return JsonConvertUtil.parseJson(body, type);
        } else if (type instanceof Class) {
            return (T) JsonUtil.parseJson(body, (Class<?>) type);
        } else if (type instanceof TypeReference) {
            return (T) JsonUtil.parseJson(body, (TypeReference<?>) type);
        } else {
            throw new IllegalArgumentException("not support type: " + type);
        }
    }

    protected void validateResponse(HttpRequestParamNew httpRequestParam, HttpResult response) {
        if (!Objects.equals(response.getStatusCode(), 200)) {
            log.warn("调用接口非200返回，请求:{} 响应:{}", httpRequestParam, response);
        }
    }

    private String replacePlaceholders(String url, MultiValuedMap<String, String> requestParam) {
        if (requestParam == null || requestParam.isEmpty()) {
            return url;
        }
        Map<String, String> params = HttpRequestParamUtils.convertMultiValuedMapToMap(requestParam);
        Pattern pattern = Pattern.compile("\\{([^}]+)}");
        Matcher matcher = pattern.matcher(url);
        StringBuffer result = new StringBuffer();

        while (matcher.find()) {
            // 拿到括号内的内容
            String key = matcher.group(1);
            String rawValue = params.getOrDefault(key, "");
            matcher.appendReplacement(result, Matcher.quoteReplacement(rawValue));
        }
        matcher.appendTail(result);
        return result.toString();
    }
}
