package com.deepinnet.localdata.integration.model.output;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Description: 船舶数据记录
 * Date: 2025/9/8
 * Author: qoder
 */
@Data
public class ShipDataRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 船舶状态
     */
    @JsonProperty("shipstatus")
    private String shipStatus;

    /**
     * UTC 日期
     */
    @JsonProperty("date")
    private String date;

    /**
     * 上传来的人数
     */
    @JsonProperty("rs")
    private String rs;

    /**
     * Z轴数据
     */
    @JsonProperty("star")
    private String star;

    /**
     * 东经或西经指示
     */
    @JsonProperty("e")
    private String e;

    /**
     * 地面速率
     */
    @JsonProperty("spd")
    private String spd;

    /**
     * 船舶类别
     */
    @JsonProperty("shipkind")
    private String shipKind;

    /**
     * 分区
     */
    @JsonProperty("data_dt_iso")
    private String dataDtIso;

    /**
     * 经度
     */
    @JsonProperty("lon")
    private String lon;

    /**
     * 电池电量
     */
    @JsonProperty("battery")
    private String battery;

    /**
     * 北纬或南纬指示
     */
    @JsonProperty("n")
    private String n;

    /**
     * 船主
     */
    @JsonProperty("shipowner")
    private String shipOwner;

    /**
     * 定位系统标识
     */
    @JsonProperty("renshu")
    private String renShu;

    /**
     * 校验和
     */
    @JsonProperty("cs")
    private String cs;

    /**
     * 采集时间
     */
    @JsonProperty("cjsj")
    private String cjsj;

    /**
     * 地面航向
     */
    @JsonProperty("cog")
    private String cog;

    /**
     * 海拔高度
     */
    @JsonProperty("hb")
    private String hb;

    /**
     * UTC 时间
     */
    @JsonProperty("time")
    private String time;

    /**
     * 消息发送时间
     */
    @JsonProperty("sendtime")
    private String sendTime;

    /**
     * 纬度
     */
    @JsonProperty("lat")
    private String lat;

    /**
     * 船舶编码
     */
    @JsonProperty("shipid")
    private String shipId;

    /**
     * 位置有效标识
     */
    @JsonProperty("status")
    private String status;
}
