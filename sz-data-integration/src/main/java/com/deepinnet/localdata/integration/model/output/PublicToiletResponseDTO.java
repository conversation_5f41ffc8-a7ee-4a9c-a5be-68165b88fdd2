package com.deepinnet.localdata.integration.model.output;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Description: 公厕信息响应DTO
 * Date: 2025/8/27
 * Author: qoder
 */
@Data
public class PublicToiletResponseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 获取数据Json响应
     */
    @JsonProperty("getDataJsonResponse")
    private GetDataJsonResponse getDataJsonResponse;

    @Data
    public static class GetDataJsonResponse implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 返回状态信息（注意：这里是字符串，不是对象）
         */
        @JsonProperty("return")
        private String returnInfo;

        /**
         * 数据内容
         */
        private DataInfo data;
    }

    @Data
    public static class ReturnInfo implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 状态码：1：成功，-1：失败，401：token有误
         */
        private String code;

        /**
         * 状态信息
         */
        private String message;

        /**
         * 是否支持搜索
         */
        @JsonProperty("hasSearch")
        private Boolean hasSearch;

        /**
         * 真实数据类型
         */
        @JsonProperty("realDataType")
        private Integer realDataType;

        /**
         * 错误标识
         */
        @JsonProperty("errorFlag")
        private Integer errorFlag;

        /**
         * 是否为文件
         */
        @JsonProperty("isFile")
        private Boolean isFile;
    }

    @Data
    public static class DataInfo implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 总记录数
         */
        private Integer totalRecords;

        /**
         * 错误标识，0表示正常，1表示错误
         */
        private Integer errorFlag;

        /**
         * 数据结果列表
         */
        private List<ToiletRecord> result;
    }

    @Data
    public static class ToiletRecord implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 公厕ID
         */
        private String toiletId;

        /**
         * 公厕名称
         */
        private String toiletName;

        /**
         * 公厕编号
         */
        private String toiletCode;

        /**
         * 公厕类型
         */
        private String toiletType;

        /**
         * 公厕等级
         */
        private String toiletGrade;

        /**
         * 详细地址
         */
        private String address;

        /**
         * 所属区域
         */
        private String district;

        /**
         * 所属街道
         */
        private String street;

        /**
         * 经度
         */
        private String longitude;

        /**
         * 纬度
         */
        private String latitude;

        /**
         * 开放时间
         */
        private String openHours;

        /**
         * 是否24小时开放
         */
        private String is24Hours;

        /**
         * 管理机构
         */
        private String managementUnit;

        /**
         * 管理员姓名
         */
        private String managerName;

        /**
         * 联系电话
         */
        private String contactPhone;

        /**
         * 建设年份
         */
        private String constructionYear;

        /**
         * 建筑面积
         */
        private String buildingArea;

        /**
         * 男厕位数
         */
        private String maleToiletCount;

        /**
         * 女厕位数
         */
        private String femaleToiletCount;

        /**
         * 无障碍设施
         */
        private String barrierFreeAccess;

        /**
         * 是否有母婴室
         */
        private String hasMaternalRoom;

        /**
         * 是否有第三卫生间
         */
        private String hasThirdToilet;

        /**
         * 清洁频次
         */
        private String cleaningFrequency;

        /**
         * 服务质量等级
         */
        private String serviceGrade;

        /**
         * 设施设备状况
         */
        private String facilityCondition;

        /**
         * 是否提供免费厕纸
         */
        private String freeToiletPaper;

        /**
         * 是否提供洗手液
         */
        private String handSoap;

        /**
         * 是否有烘手器
         */
        private String handDryer;

        /**
         * 是否有空调
         */
        private String hasAirConditioning;

        /**
         * 是否有WiFi
         */
        private String hasWifi;

        /**
         * 是否有监控设备
         */
        private String hasMonitoring;

        /**
         * 停车位数量
         */
        private String parkingSpaces;

        /**
         * 周边环境描述
         */
        private String surroundingEnvironment;

        /**
         * 交通便利程度
         */
        private String transportationAccess;

        /**
         * 运行状态
         */
        private String operationStatus;

        /**
         * 维护状态
         */
        private String maintenanceStatus;

        /**
         * 上次维护时间
         */
        private String lastMaintenanceTime;

        /**
         * 下次维护计划
         */
        private String nextMaintenanceTime;

        /**
         * 使用人流量（日均）
         */
        private String dailyTraffic;

        /**
         * 服务评价
         */
        private String serviceRating;

        /**
         * 投诉建议
         */
        private String complaints;

        /**
         * 数据来源
         */
        private String dataSource;

        /**
         * 创建时间
         */
        private String createTime;

        /**
         * 更新时间
         */
        private String updateTime;

        /**
         * 备注
         */
        private String remark;
    }
}