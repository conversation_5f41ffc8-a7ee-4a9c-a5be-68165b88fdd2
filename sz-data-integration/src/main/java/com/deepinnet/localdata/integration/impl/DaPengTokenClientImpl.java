package com.deepinnet.localdata.integration.impl;

import com.deepinnet.localdata.integration.DaPengTokenClient;
import com.deepinnet.localdata.integration.constants.ActionConstants;
import com.deepinnet.localdata.integration.http.HttpRequestParam;
import com.deepinnet.localdata.integration.model.input.OAuth2TokenRequestDTO;
import com.deepinnet.localdata.integration.model.output.OAuth2TokenResponseDTO;
import com.deepinnet.localdata.integration.service.DaPengApiConnectionInfo;
import com.deepinnet.localdata.integration.service.DaPengTokenHttpRequestService;
import com.deepinnet.localdata.integration.util.CryptoUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 大鹏Token获取客户端实现
 * Date: 2025/9/1
 * Author: qoder
 */
@Service
@Slf4j
public class DaPengTokenClientImpl implements DaPengTokenClient {

    @Resource
    private DaPengTokenHttpRequestService tokenHttpRequestService;

    @Resource
    private DaPengApiConnectionInfo connectInfo;

    /**
     * Token缓存（内存缓存，生产环境建议使用Redis）
     */
    private static final Map<String, String> TOKEN_CACHE = new ConcurrentHashMap<>();
    private static final Map<String, Long> TOKEN_EXPIRE_TIME = new ConcurrentHashMap<>();

    @Override
    public OAuth2TokenResponseDTO getDaPengToken(OAuth2TokenRequestDTO request) {
        try {
            log.info("开始获取大鹏Token，clientId: {}", request.getClientId());

            // 参数验证
            if (!StringUtils.hasText(request.getClientId()) || !StringUtils.hasText(request.getClientSecret())) {
                log.warn("客户端ID或密钥为空");
                return OAuth2TokenResponseDTO.error("0");
            }

            // 检查缓存中是否有有效token
            String cacheKey = request.getClientId();
            String cachedToken = TOKEN_CACHE.get(cacheKey);
            Long expireTime = TOKEN_EXPIRE_TIME.get(cacheKey);

            if (StringUtils.hasText(cachedToken) && expireTime != null && expireTime > System.currentTimeMillis()) {
                log.info("使用缓存的Token: {}", cachedToken);
                return OAuth2TokenResponseDTO.success(cachedToken,
                        String.valueOf((expireTime - System.currentTimeMillis()) / 1000));
            }

            // 构建请求参数（保持兼容原有OAuth2方式）
            HttpRequestParam httpRequestParam = HttpRequestParam.builder()
                    .action(ActionConstants.DA_PENG_GET_TOKEN)
                    .method(com.deepinnet.localdata.integration.http.HttpMethodEnum.GET)
                    .build();

            Map<String, Object> params = new HashMap<>();
            params.put("grant_type", request.getGrantType());
            params.put("client_id", request.getClientId());
            params.put("client_secret", request.getClientSecret());

            httpRequestParam.setRequestParam(com.deepinnet.digitaltwin.common.util.JsonUtil.toJsonStr(params));

            // 发送请求获取Token
            OAuth2TokenResponseDTO response = tokenHttpRequestService.httpRequest(httpRequestParam,
                    OAuth2TokenResponseDTO.class);

            // 缓存Token
            if (response != null && response.getCustom() != null
                    && StringUtils.hasText(response.getCustom().getAccessToken())) {
                String accessToken = response.getCustom().getAccessToken();
                Long expiresIn = Long.parseLong(response.getCustom().getExpiresIn());
                Long expireTimeMs = System.currentTimeMillis() + (expiresIn * 1000);

                TOKEN_CACHE.put(cacheKey, accessToken);
                TOKEN_EXPIRE_TIME.put(cacheKey, expireTimeMs);

                log.info("大鹏Token获取成功并已缓存，clientId: {}", request.getClientId());
            }

            return response;

        } catch (Exception e) {
            log.error("获取大鹏Token失败", e);
            return OAuth2TokenResponseDTO.error("0");
        }
    }

    /**
     * 获取大鹏Token（使用用户名密码方式）
     * 接口格式：/gateway/generateToken?userName=用户名&password=密码(明文)&pwd=密码(MD5加密)&ip=&time=86400
     */
    public OAuth2TokenResponseDTO getDaPengTokenByUserPassword(String userName, String password) {
        return getDaPengTokenByUserPassword(userName, password, null);
    }

    /**
     * 获取大鹏Token（使用用户名密码方式，支持预设 MD5 密码）
     * 接口格式：/gateway/generateToken?userName=用户名&password=密码(明文)&pwd=密码(MD5加密)&ip=&time=86400
     *
     * @param userName  用户名
     * @param password  明文密码
     * @param presetMd5 预设的MD5密码，如为null则动态计算
     * @return Token响应结果
     */
    public OAuth2TokenResponseDTO getDaPengTokenByUserPassword(String userName, String password, String presetMd5) {
        try {
            log.info("开始获取大鹏Token（用户名密码方式），userName: {}", userName);

            // 参数验证 - 只要求用户名，密码可为可选（如果有MD5密码）
            if (!StringUtils.hasText(userName)) {
                log.warn("用户名为空");
                return OAuth2TokenResponseDTO.error("用户名不能为空");
            }

            // 检查缓存中是否有有效token
            String cacheKey = userName;
            String cachedToken = TOKEN_CACHE.get(cacheKey);
            Long expireTime = TOKEN_EXPIRE_TIME.get(cacheKey);

            if (StringUtils.hasText(cachedToken) && expireTime != null && expireTime > System.currentTimeMillis()) {
                log.info("使用缓存的Token: {}", cachedToken);
                return OAuth2TokenResponseDTO.success(cachedToken,
                        String.valueOf((expireTime - System.currentTimeMillis()) / 1000));
            }

            // 获取MD5加密密码
            String md5Password;
            if (StringUtils.hasText(presetMd5)) {
                // 优先使用传入的预设 MD5 密码
                md5Password = presetMd5;
                log.debug("使用传入的预设 MD5 密码");
            } else if (StringUtils.hasText(connectInfo.getPasswordMd5())) {
                // 使用配置中预设的MD5密码
                md5Password = connectInfo.getPasswordMd5();
                log.debug("使用配置中的MD5密码");
            } else if (StringUtils.hasText(password)) {
                // 降级：动态生成MD5加密密码
                md5Password = CryptoUtils.md5Encrypt(password);
                log.debug("动态生成MD5密码");
            } else {
                log.error("无法获取MD5密码：既没有预设的MD5密码，也没有明文密码用于动态生成");
                return OAuth2TokenResponseDTO.error("缺少必要的密码信息");
            }

            // 构建请求参数（使用GET请求参数方式）
            HttpRequestParam httpRequestParam = HttpRequestParam.builder()
                    .action(ActionConstants.DA_PENG_GET_TOKEN)
                    .method(com.deepinnet.localdata.integration.http.HttpMethodEnum.GET)
                    .build();

            Map<String, Object> params = new HashMap<>();
            params.put("userName", userName);

            // 只在有明文密码时才添加password参数
            if (StringUtils.hasText(password)) {
                params.put("password", password); // 明文密码
                log.debug("添加明文密码参数");
            } else {
                // 如果没有明文密码，可以传递空字符串或不传递
                params.put("password", ""); // 空字符串
                log.debug("使用空密码，仅依赖pwd参数");
            }

            params.put("pwd", md5Password); // MD5加密密码
            params.put("ip", ""); // IP地址，可为空
            params.put("time", connectInfo.getTokenTime() != null ? connectInfo.getTokenTime() : "86400");

            httpRequestParam.setRequestParam(com.deepinnet.digitaltwin.common.util.JsonUtil.toJsonStr(params));

            // 发送请求获取Token（使用专用的响应DTO）
            com.deepinnet.localdata.integration.model.output.DaPengTokenResponseDTO tokenResponse = tokenHttpRequestService
                    .httpRequest(httpRequestParam,
                            com.deepinnet.localdata.integration.model.output.DaPengTokenResponseDTO.class);

            if (tokenResponse != null && tokenResponse.isSuccess() && StringUtils.hasText(tokenResponse.getToken())) {
                String token = tokenResponse.getToken();
                // 缓存Token（默认有效期24小时）
                long tokenTimeSeconds = Long
                        .parseLong(connectInfo.getTokenTime() != null ? connectInfo.getTokenTime() : "86400");
                Long expireTimeMs = System.currentTimeMillis() + (tokenTimeSeconds * 1000);

                TOKEN_CACHE.put(cacheKey, token);
                TOKEN_EXPIRE_TIME.put(cacheKey, expireTimeMs);

                log.info("大鹏Token获取成功并已缓存，userName: {}, token: {}", userName,
                        token.substring(0, Math.min(20, token.length())) + "...");

                return OAuth2TokenResponseDTO.success(token, String.valueOf(tokenTimeSeconds));
            } else {
                String errorMsg = tokenResponse != null
                        ? String.format("Token获取失败: code=%s, message=%s", tokenResponse.getCode(),
                                tokenResponse.getMessage())
                        : "Token响应为空";
                log.error("大鹏Token获取失败: {}", errorMsg);
                return OAuth2TokenResponseDTO.error(errorMsg);
            }

        } catch (Exception e) {
            log.error("获取大鹏Token失败", e);
            return OAuth2TokenResponseDTO.error("获取Token异常: " + e.getMessage());
        }
    }

    /**
     * 获取大鹏Token（使用配置中的凭证）
     *
     * @return Token响应结果
     */
    public OAuth2TokenResponseDTO getDaPengTokenWithConfigCredentials() {
        if (!StringUtils.hasText(connectInfo.getUserName())) {
            log.warn("配置中的用户名为空");
            return OAuth2TokenResponseDTO.error("配置中的用户名不能为空");
        }

        // 检查是否有MD5密码或明文密码
        if (!StringUtils.hasText(connectInfo.getPasswordMd5()) && !StringUtils.hasText(connectInfo.getPassword())) {
            log.warn("配置中既没有MD5密码也没有明文密码");
            return OAuth2TokenResponseDTO.error("配置中缺少必要的密码信息");
        }

        return getDaPengTokenByUserPassword(
                connectInfo.getUserName(),
                connectInfo.getPassword(), // 可以为null或空字符串
                connectInfo.getPasswordMd5());
    }

    @Override
    public OAuth2TokenResponseDTO getDaPengToken() {
        // 优先使用用户名密码方式（只需要用户名和 MD5 密码配置）
        if (StringUtils.hasText(connectInfo.getUserName())) {
            return getDaPengTokenWithConfigCredentials();
        }

        // 降级到OAuth2方式
        OAuth2TokenRequestDTO request = new OAuth2TokenRequestDTO();
        request.setClientId(connectInfo.getClientId());
        request.setClientSecret(connectInfo.getClientSecret());
        request.setGrantType("client_credentials");

        return getDaPengToken(request);
    }

    @Override
    public OAuth2TokenResponseDTO refreshDaPengToken(String refreshToken) {
        // TODO: 实现刷新Token逻辑（如果大鹏接口支持）
        log.warn("刷新Token功能暂未实现");
        return OAuth2TokenResponseDTO.error("0");
    }

    /**
     * 获取当前有效的Token
     * 增强的过期处理机制：
     * 1. 检查Token是否即将过期（提前5分钟刷新）
     * 2. 自动重试机制
     * 3. 异常处理和降级
     *
     * @return 当前有效的Token，如果没有则返回null
     */
    public String getCurrentValidToken() {
        return getCurrentValidToken(true);
    }

    /**
     * 获取当前有效的Token（内部方法）
     *
     * @param allowRefresh 是否允许自动刷新
     * @return 当前有效的Token
     */
    private String getCurrentValidToken(boolean allowRefresh) {
        // 优先使用用户名密码方式
        if (StringUtils.hasText(connectInfo.getUserName())) {
            String cacheKey = connectInfo.getUserName();
            String cachedToken = TOKEN_CACHE.get(cacheKey);
            Long expireTime = TOKEN_EXPIRE_TIME.get(cacheKey);

            // 检查Token是否存在且有效
            if (StringUtils.hasText(cachedToken) && expireTime != null) {
                long currentTime = System.currentTimeMillis();
                long timeUntilExpiry = expireTime - currentTime;

                // 如果Token还有效且未即将过期（剩余时间大于5分钟）
                if (timeUntilExpiry > 5 * 60 * 1000) { // 5分钟 = 300,000毫秒
                    log.debug("Token仍然有效，剩余时间: {}秒", timeUntilExpiry / 1000);
                    return cachedToken;
                }

                // Token即将过期或已过期，需要刷新
                if (allowRefresh) {
                    log.info("Token即将过期或已过期，开始自动刷新...");
                    return refreshTokenWithRetry(cacheKey);
                } else {
                    log.warn("Token已过期且不允许刷新");
                    return null;
                }
            }

            // Token不存在，需要获取新Token
            if (allowRefresh) {
                log.info("Token不存在，开始获取新Token...");
                return refreshTokenWithRetry(cacheKey);
            }
        }

        return null;
    }

    /**
     * 带重试机制的Token刷新
     *
     * @param cacheKey 缓存键
     * @return 新的Token
     */
    private String refreshTokenWithRetry(String cacheKey) {
        int maxRetries = 3;
        int retryDelay = 1000; // 1秒

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                log.info("尝试第{}次获取Token...", attempt);

                // 获取新Token
                OAuth2TokenResponseDTO response = getDaPengToken();

                if (response != null) {
                    // 用户名密码方式返回的是String
                    if (response.getCustom() != null && StringUtils.hasText(response.getCustom().getAccessToken())) {
                        String newToken = response.getCustom().getAccessToken();
                        log.info("Token刷新成功，尝试次数: {}", attempt);
                        return newToken;
                    }
                    // 如果是直接返回String类型的Token（用户名密码方式）
                    // 这里需要修改getDaPengToken方法返回的结构
                }

                log.warn("第{}次尝试Token获取失败", attempt);

            } catch (Exception e) {
                log.error("第{}次尝试Token获取异常", attempt, e);
            }

            // 如果不是最后一次尝试，等待一段时间再重试
            if (attempt < maxRetries) {
                try {
                    Thread.sleep(retryDelay * attempt); // 递增延迟
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    log.error("Token刷新重试被中断", ie);
                    break;
                }
            }
        }

        log.error("Token刷新失败，已经尝试{}次", maxRetries);
        return null;
    }

    /**
     * 清除Token缓存
     */
    public void clearTokenCache() {
        TOKEN_CACHE.clear();
        TOKEN_EXPIRE_TIME.clear();
        log.info("Token缓存已清除");
    }
}