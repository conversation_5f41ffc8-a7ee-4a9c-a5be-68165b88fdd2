package com.deepinnet.localdata.integration.impl;

import com.deepinnet.localdata.integration.AncientVillage2Client;
import com.deepinnet.localdata.integration.constants.ActionConstants;
import com.deepinnet.localdata.integration.http.BaseHttpRequestClient;
import com.deepinnet.localdata.integration.http.HttpMethodEnum;
import com.deepinnet.localdata.integration.http.HttpRequestParamNew;
import com.deepinnet.localdata.integration.model.input.AncientVillage2QueryDTO;
import com.deepinnet.localdata.integration.model.output.AncientVillage2ResponseDTO;
import com.deepinnet.localdata.integration.model.output.AncientVillage2RealResponseDTO;
import com.deepinnet.localdata.integration.impl.DaPengTokenClientImpl;
import com.deepinnet.digitaltwin.common.util.JsonUtil;
import org.apache.commons.collections4.MultiValuedMap;
import org.apache.commons.collections4.multimap.ArrayListValuedHashMap;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 古村落2信息Client实现
 * Date: 2024/8/29
 * Author: lijunheng
 */
@Component
public class AncientVillage2ClientImpl extends BaseHttpRequestClient implements AncientVillage2Client {

    private DaPengTokenClientImpl daPengTokenClient;

    // 移除无参构造函数，只保留有参构造函数
    public AncientVillage2ClientImpl(DaPengTokenClientImpl daPengTokenClient) {
        this.daPengTokenClient = daPengTokenClient;
    }

    @Override
    public AncientVillage2ResponseDTO getAncientVillage2Data(AncientVillage2QueryDTO queryDTO) {
        System.out.println("开始调用古村落2信息接口获取数据，参数: " + queryDTO);

        // 直接在这里替换Token占位符
        String action = ActionConstants.ANCIENT_VILLAGE2_GET_DATA_JSON;
        if (action.contains(ActionConstants.DA_PENG_TOKEN_PLACEHOLDER)) {
            // 获取当前有效的Token
            String token = daPengTokenClient.getCurrentValidToken();
            if (token != null && !token.isEmpty()) {
                // 替换占位符
                action = action.replace(ActionConstants.DA_PENG_TOKEN_PLACEHOLDER, token);
                System.out.println("Token替换完成: " + ActionConstants.ANCIENT_VILLAGE2_GET_DATA_JSON + " -> " + action);
            } else {
                System.out.println("无法获取有效Token，使用原始URL");
            }
        }

        // 构建查询参数
        MultiValuedMap<String, String> paramMap = new ArrayListValuedHashMap<>();
        paramMap.put("pageNo", queryDTO.getPageNo() != null ? queryDTO.getPageNo() : "1");
        paramMap.put("pageSize",
                queryDTO.getPageSize() != null ? queryDTO.getPageSize() : "10");
        if (queryDTO.getSearch() != null) {
            paramMap.put("search", queryDTO.getSearch());
        }

        // 构建HTTP请求参数
        HttpRequestParamNew param = HttpRequestParamNew.builder()
                .method(HttpMethodEnum.GET)
                .action(action)
                .address("http://************:8280") // 数据服务器地址
                .requestParam(paramMap)
                .build();

        // 使用新的DTO类解析响应
        AncientVillage2RealResponseDTO realResponse = httpRequest(param, AncientVillage2RealResponseDTO.class,
                null);

        // 转换为原来的DTO格式（如果需要的话）
        System.out.println("结束调用古村落2信息接口获取数据");
        return convertToAncientVillage2ResponseDTO(realResponse);
    }

    /**
     * 将实际响应转换为接口定义的响应格式
     * 
     * @param realResponse 实际响应
     * @return 接口定义的响应格式
     */
    private AncientVillage2ResponseDTO convertToAncientVillage2ResponseDTO(
            AncientVillage2RealResponseDTO realResponse) {
        if (realResponse == null) {
            return null;
        }

        AncientVillage2ResponseDTO response = new AncientVillage2ResponseDTO();
        AncientVillage2ResponseDTO.GetDataJsonResponse getDataJsonResponse = new AncientVillage2ResponseDTO.GetDataJsonResponse();

        // 直接设置返回信息字符串（不再尝试解析为对象）
        if (realResponse.getGetDataJsonResponse() != null) {
            getDataJsonResponse.setReturnInfo(realResponse.getGetDataJsonResponse().getReturnInfo());

            // 设置数据信息
            AncientVillage2RealResponseDTO.DataInfo realData = realResponse.getGetDataJsonResponse().getData();
            if (realData != null) {
                AncientVillage2ResponseDTO.DataInfo dataInfo = new AncientVillage2ResponseDTO.DataInfo();
                dataInfo.setTotalRecords(realData.getTotalRecords());
                dataInfo.setErrorFlag(realResponse.getGetDataJsonResponse().getErrorFlag());

                // 转换记录列表
                if (realData.getResult() != null) {
                    List<AncientVillage2ResponseDTO.AncientVillage2Record> records = new ArrayList<>();
                    for (AncientVillage2RealResponseDTO.AncientVillage2Record realRecord : realData.getResult()) {
                        AncientVillage2ResponseDTO.AncientVillage2Record record = new AncientVillage2ResponseDTO.AncientVillage2Record();
                        // 这里可以根据需要映射字段
                        record.setVillageName(realRecord.getName());
                        record.setLongitude(realRecord.getLon());
                        record.setLatitude(realRecord.getLat());
                        record.setDetailedAddress(realRecord.getGeneral_situation());
                        record.setOpeningHours(realRecord.getBusiness_hours());
                        record.setTicketPrice(realRecord.getTickets_price());
                        record.setBestVisitingSeason(realRecord.getBest_tourist_season());
                        record.setMajorAttractions(realRecord.getSpecial_sceni_cspot());
                        record.setTransportationAccessibility(realRecord.getTraffic_accessibility());
                        record.setManagementInstitution(realRecord.getManage_name());
                        records.add(record);
                    }
                    dataInfo.setResult(records);
                }

                getDataJsonResponse.setData(dataInfo);
            }
        }

        response.setGetDataJsonResponse(getDataJsonResponse);
        return response;
    }

    @Override
    public AncientVillage2ResponseDTO getAncientVillage2Data(String pageNo, String pageSize, String search) {
        AncientVillage2QueryDTO queryDTO = new AncientVillage2QueryDTO();
        queryDTO.setPageNo(pageNo);
        queryDTO.setPageSize(pageSize);
        queryDTO.setSearch(search);
        return getAncientVillage2Data(queryDTO);
    }

    @Override
    public AncientVillage2ResponseDTO getAncientVillage2Data() {
        return getAncientVillage2Data("1", "10", "");
    }
}