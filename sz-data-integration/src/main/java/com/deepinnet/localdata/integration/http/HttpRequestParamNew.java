package com.deepinnet.localdata.integration.http;

import com.deepinnet.localdata.integration.constants.ActionConstants;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.collections4.MultiValuedMap;

import java.io.Serializable;

/**
 * Description: http请求参数
 *
 * Date: 2024/8/28
 * Author: lijunheng
 */
@Data
@Builder
public class HttpRequestParamNew implements Serializable {

    /**
     * 地址前缀，例如http://www.baidu.com
     */
    private String address;

    /**
     * 行为，就是调用请求URL
     * {@link ActionConstants}
     */
    private String action;

    private HttpMethodEnum method;

    private MultiValuedMap<String, String> requestHeader;

    private MultiValuedMap<String, String> requestParam;

    private Object requestBody;
}
