package com.deepinnet.localdata.integration.config;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.ContextRefreshedEvent;

/**
 * Description: 外部接口实现都是不一样的，需要根据具体运行环境做匹配，如果匹配不到就走默认的代理客户端抛出不支持的异常
 * 不怕繁琐的话也可以用@ConditionalOnMissingBean注解代替
 *
 * Date: 2024/12/25
 * Author: lijunheng
 */
@Configuration
public class GenerateDefaultClientBeanFactoryPostProcess implements ApplicationListener<ContextRefreshedEvent> {

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        System.out.println("[DEBUG] GenerateDefaultClientBeanFactoryPostProcess - 开始处理Bean注册");

        ApplicationContext applicationContext = event.getApplicationContext();
        ConfigurableListableBeanFactory beanFactory = (ConfigurableListableBeanFactory) applicationContext
                .getAutowireCapableBeanFactory();

        // 打印所有已注册的Bean信息
        String[] allBeanNames = beanFactory.getBeanDefinitionNames();
        System.out.println("[DEBUG] 总共注册了 " + allBeanNames.length + " 个Bean");

        // 查找实现类Bean
        System.out.println("[DEBUG] 查找客户端实现类:");
        for (String beanName : allBeanNames) {
            if (beanName.toLowerCase().contains("client")) {
                System.out.println("[DEBUG] 找到客户端Bean: " + beanName + " -> " + beanFactory.getType(beanName));
            }
        }

        // 直接检查我们知道的带有@GenerateDefaultClient注解的接口
        checkAndCreateDefaultProxy(beanFactory, "com.deepinnet.localdata.integration.BoatEquipmentClient");
        checkAndCreateDefaultProxy(beanFactory, "com.deepinnet.localdata.integration.DeviceAlarmClient");
        checkAndCreateDefaultProxy(beanFactory, "com.deepinnet.localdata.integration.BoatRescueAlarmClient");
        checkAndCreateDefaultProxy(beanFactory, "com.deepinnet.localdata.integration.BoatDataReportClient");
        checkAndCreateDefaultProxy(beanFactory, "com.deepinnet.localdata.integration.BoatStatusClient");
        checkAndCreateDefaultProxy(beanFactory, "com.deepinnet.localdata.integration.ParameterSettingsClient");
        checkAndCreateDefaultProxy(beanFactory, "com.deepinnet.localdata.integration.PublicToiletClient");
        checkAndCreateDefaultProxy(beanFactory, "com.deepinnet.localdata.integration.HospitalClient");
        checkAndCreateDefaultProxy(beanFactory, "com.deepinnet.localdata.integration.EntertainmentVenueClient");
        checkAndCreateDefaultProxy(beanFactory, "com.deepinnet.localdata.integration.ParkingLotClient");
        checkAndCreateDefaultProxy(beanFactory, "com.deepinnet.localdata.integration.AncientVillageClient");
        checkAndCreateDefaultProxy(beanFactory, "com.deepinnet.localdata.integration.AncientVillage2Client");
        checkAndCreateDefaultProxy(beanFactory, "com.deepinnet.localdata.integration.ScenicAreaClient");
        checkAndCreateDefaultProxy(beanFactory, "com.deepinnet.localdata.integration.TravelAgencyClient");
        checkAndCreateDefaultProxy(beanFactory, "com.deepinnet.localdata.integration.ShipDataClient");
        checkAndCreateDefaultProxy(beanFactory, "com.deepinnet.localdata.integration.CaseReportClient");
        checkAndCreateDefaultProxy(beanFactory, "com.deepinnet.localdata.integration.EventReportItemsClient");
        checkAndCreateDefaultProxy(beanFactory, "com.deepinnet.localdata.integration.FileUploadClient");
        checkAndCreateDefaultProxy(beanFactory, "com.deepinnet.localdata.integration.OAuth2Client");

        System.out.println("[DEBUG] GenerateDefaultClientBeanFactoryPostProcess - Bean注册处理完成");
    }

    private void checkAndCreateDefaultProxy(ConfigurableListableBeanFactory beanFactory, String className) {
        try {
            System.out.println("[DEBUG] 检查类: " + className);
            Class<?> clazz = Class.forName(className);

            // 检查是否有@GenerateDefaultClient注解
            if (clazz.isAnnotationPresent(GenerateDefaultClient.class)) {
                System.out.println("[DEBUG] 类 " + clazz.getSimpleName() + " 有@GenerateDefaultClient注解");

                // 检查容器中是否已经存在该Bean
                String[] beanNames = beanFactory.getBeanNamesForType(clazz);
                System.out.println("[DEBUG] 为类 " + clazz.getSimpleName() + " 找到 " + beanNames.length + " 个Bean: "
                        + java.util.Arrays.toString(beanNames));

                if (beanNames.length == 0) {
                    String beanName = clazz.getSimpleName().substring(0, 1).toLowerCase()
                            + clazz.getSimpleName().substring(1);
                    System.out.println("[DEBUG] 没有找到实现Bean，创建默认代理Bean: " + beanName);
                    beanFactory.registerSingleton(beanName, DefaultClientProxy.createDefaultClient(clazz));
                    System.out.println(
                            "[INFO] Created default proxy for: " + clazz.getSimpleName() + " as bean: " + beanName);
                } else {
                    System.out.println("[INFO] Found existing implementation for: " + clazz.getSimpleName() + " -> "
                            + java.util.Arrays.toString(beanNames));

                    // 检查每个Bean的实际类型
                    for (String beanName : beanNames) {
                        Object bean = beanFactory.getBean(beanName);
                        System.out.println("[DEBUG] Bean " + beanName + " 实际类型: " + bean.getClass().getName());
                    }
                }
            } else {
                System.out.println("[DEBUG] 类 " + clazz.getSimpleName() + " 没有@GenerateDefaultClient注解");
            }
        } catch (ClassNotFoundException e) {
            // 如果类不存在，跳过
            System.out.println("[DEBUG] Skipping non-existent class: " + className);
        } catch (Exception e) {
            System.err.println("[ERROR] Error processing class " + className + ": " + e.getMessage());
            e.printStackTrace();
        }
    }
}
