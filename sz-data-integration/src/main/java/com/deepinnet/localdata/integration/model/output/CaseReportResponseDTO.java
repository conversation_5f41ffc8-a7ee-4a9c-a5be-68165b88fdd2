package com.deepinnet.localdata.integration.model.output;

import lombok.Data;

/**
 * 工单上报响应DTO
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
public class CaseReportResponseDTO {

    /**
     * 自定义数据
     */
    private Custom custom;

    /**
     * 状态信息
     */
    private Status status;

    @Data
    public static class Custom {
        /**
         * 工单编码
         */
        private String casecode;
    }

    @Data
    public static class Status {
        /**
         * 状态码
         */
        private String code;

        /**
         * 状态描述
         */
        private String text;
    }

    /**
     * 创建成功响应
     */
    public static CaseReportResponseDTO success(String casecode) {
        CaseReportResponseDTO response = new CaseReportResponseDTO();

        Custom custom = new Custom();
        custom.setCasecode(casecode);
        response.setCustom(custom);

        Status status = new Status();
        status.setCode("1");
        status.setText("请求成功");
        response.setStatus(status);

        return response;
    }

    /**
     * 创建失败响应
     */
    public static CaseReportResponseDTO error(String errorMessage) {
        CaseReportResponseDTO response = new CaseReportResponseDTO();

        Custom custom = new Custom();
        custom.setCasecode("");
        response.setCustom(custom);

        Status status = new Status();
        status.setCode("0");
        status.setText(errorMessage != null ? errorMessage : "请求失败");
        response.setStatus(status);

        return response;
    }
}