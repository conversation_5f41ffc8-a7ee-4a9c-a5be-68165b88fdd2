package com.deepinnet.localdata.integration.constants;

/**
 * Description:
 * Date: 2024/8/28
 * Author: lijunheng
 */
public class ActionConstants {

    /*--------------------------Token占位符------------------------------------------*/
    /**
     * 大鹏Token占位符，将在运行时动态替换为实际Token
     */
    public static final String DA_PENG_TOKEN_PLACEHOLDER = "{DYNAMIC_TOKEN}";

    /*--------------------------登陆------------------------------------------*/
    public static final String LOGIN_AUTH = "/videoService/accounts/authorize";

    public static final String LOGIN_KEEP_ALIVE = "/videoService/accounts/token/keepalive";

    public static final String LOGOUT = "/videoService/accounts/unauthorize";

    /*--------------------------摄像头------------------------------------------*/
    public static final String REAL_MONITOR = "/videoService/realmonitor/uri";

    public static final String PLAYBACK = "/videoService/playback/uri";

    public static final String RECORD = "/videoService/record/records";

    /**
     * 获取设备详细信息
     */
    public static final String DEVICE_DETAIL = "/videoService/devicesManager/devices";

    /**
     * 获取设备详细信息接口参数的后缀
     */
    public static final String DEVICE_DETAIL_QUERY_SUFFIX = "D";

    /*--------------------------过车------------------------------------------*/
    public static final String CHANNEL_FLOW_COUNT = "/vehicleTactics/rest/picReport/channelFlowCount";

    public static final String VEHICLE_PASS_DETAIL = "/vehicleService/rest/vehicle/query";

    public static final String VEHICLE_PASS_QUERY = "/vehicleService/rest/vehicle/queryList";

    /*--------------------------交警------------------------------------------*/
    public static final String TRAFFIC_EVENT = "/ZAZT-API-DATASOURCE-SERVICE/datasourceSearch/searchBySQL/330800230000-0100-00111";

    /*--------------------------对讲机------------------------------------------*/
    public static final String INTERCOM_350M = "/ZAZT-API-DATASOURCE-SERVICE/datasourceSearch/searchBySQL/330800230000-0100-00110";

    public static final String PUBLIC_INTERCOM = "/ZAZT-API-DATASOURCE-SERVICE/datasourceSearch/searchBySingleTable/330800230000-09200-00109";

    /*--------------------------空域管理------------------------------------------*/

    public static final String LOGIN_TOKEN = "/api/auth/oauth/token";

    /**
     * 查询所有空域
     */
    public static final String AIRSPACE_ALL_ENABLE_LIST = "/api/space/airspace/pageList";

    /**
     * 查询禁飞区
     */
    public static final String NO_FLY_ZONE_LIST = "/api/space/fence/list";

    /**
     * 查询起降点
     */
    public static final String LANDING_POINT_LIST = "/api/space/landingPoint/page";

    /**
     * 查询临时管制情报
     */
    public static final String TEMP_FENCE_LIST = "/api/space/fence/list";

    /**
     * 气象雷达获取所有数据
     */
    public static final String WEATHER_RADAR_GET_ALL = "/api/device/weatherRadar/getAll";

    /**
     * 获取近一月飞行服务公司统计信息
     */
    public static final String FLIGHT_SERVICE_COMPANY_STATISTICS = "/api/flight/statistics/getFlightServiceCompanyStatistics";

    /**
     * 获取飞行业务分析统计信息
     */
    public static final String FLIGHT_BUSINESS_ANALYSIS = "/api/task/task/data-statistics/getFlightBusinessAnalysis";

    /**
     * 获取飞行任务性质统计信息
     */
    public static final String FLIGHT_TASK_NATURE_STATISTICS = "/api/flight/statistics/getFlightTaskNatureStatistics";

    /**
     * 获取飞行数据总览统计信息
     */
    public static final String FLIGHT_DATA_OVERVIEW = "/api/flight/statistics/getFlightDataOverview";

    /**
     * 查询指定区域内的监视数据
     */
    public static final String SURVEILLANCE_QUERY = "/api/device/surveillance/query";

    /*--------------------------雷达管理------------------------------------------*/

    /**
     * 查询所有雷达设备
     */
    public static final String RADAR_LIST = "/api/device/radar";

    /*--------------------------导航设备管理------------------------------------------*/

    /**
     * 导航设备获取所有数据
     */
    public static final String NAVIGATE_EQUIPMENT_GET_ALL = "/api/device/navigateEquip/getAll";

    /**
     * 导航设备查看详情
     */
    public static final String NAVIGATE_EQUIPMENT_DETAIL = "/api/device/navigateEquip/";

    /*--------------------------基站管理------------------------------------------*/

    /**
     * 基站查询获取所有数据
     */
    public static final String BASE_STATION_GET_ALL = "/api/device/baseStation/getAll";

    /**
     * 基站查看详情
     */
    public static final String BASE_STATION_DETAIL = "/api/device/baseStation/";

    /**
     * 侦测定位设备获取所有数据接口
     */
    public static final String DETECTED_DEVICE_GET_ALL = "/api/device/detectedDevice/getAll";

    /**
     * 侦测定位设备详情接口
     */
    public static final String DETECTED_DEVICE_DETAIL = "/api/device/detectedDevice/{id}";

    /*--------------------------飞行计划------------------------------------------*/
    /**
     * 查询所有的飞行计划
     */
    public static final String FLIGHT_PLAN = "/api/plan/flyRelation/index/queryList";

    public static final String FLIGHT_PLAN_DETAIL = "/api/plan/flightPlan/detail/";

    public static final String FLIGHT_LIVE_VIDEO = "/api/device/live-address/getBySn";

    /*--------------------------飞行需求------------------------------------------*/

    public static final String FLIGHT_DEMAND_CREATE = "/api/task/demand/insertDemand";

    /*--------------------------鸳飞------------------------------------------*/
    public static final String LOGIN_VERIFY_CODE = "/api/notice/sms/image-code";

    /*--------------------------高德企业地图------------------------------------------*/
    public static final String GAO_DE_LOCATION_NAME_BY_POINT = "/gss/geocode/v2";

    public static final String GAO_DE_LOCATION_POI_BY_KEY = "/as/search/poi";

    /**
     * 起降场分页获取数据接口
     */
    public static final String LANDING_POINT_PAGE = "/api/space/landingPoint/page";

    /**
     * 起降场详情接口
     */
    public static final String LANDING_POINT_DETAIL = "/api/space/landingPoint/{id}";

    /*--------------------------大鹏接口------------------------------------------*/

    /**
     * 大鹏获取Token接口
     */
    public static final String DA_PENG_GET_TOKEN = "/gateway/generateToken";

    /**
     * 休闲船舶、游艇船载设备对应表获取数据接口
     */
    public static final String BOAT_EQUIPMENT_GET_DATA_JSON = "/services/DYWHSJ_DB_3/xxcbytczsbdyb/"
            + DA_PENG_TOKEN_PLACEHOLDER + "/getDataJson";

    /**
     * 设备报警信息获取数据接口
     */
    public static final String DEVICE_ALARM_GET_DATA_JSON = "/services/DYWHSJ_DB_2/sbbjxx/" + DA_PENG_TOKEN_PLACEHOLDER
            + "/getDataJson";

    /**
     * 船舶求助报警信息获取数据接口
     */
    public static final String BOAT_RESCUE_ALARM_GET_DATA_JSON = "/services/DYWHSJ_DB_1/cbqzbjxx/"
            + DA_PENG_TOKEN_PLACEHOLDER + "/getDataJson";

    /**
     * 船舶数据上报信息获取数据接口
     */
    public static final String BOAT_DATA_REPORT_GET_DATA_JSON = "/services/DYWHSJ_DB_4/cbsjsbxx/"
            + DA_PENG_TOKEN_PLACEHOLDER + "/getDataJson";

    /**
     * 船舶状态信息获取数据接口
     */
    public static final String BOAT_STATUS_GET_DATA_JSON = "/services/DYWHSJ_DB_6/cbztxx/" + DA_PENG_TOKEN_PLACEHOLDER
            + "/getDataJson";

    /**
     * 参数设置信息获取数据接口
     */
    public static final String PARAMETER_SETTINGS_GET_DATA_JSON = "/services/DYWHSJ_DB_5/cssz/"
            + DA_PENG_TOKEN_PLACEHOLDER + "/getDataJson";

    /**
     * 公厕信息获取数据接口
     */
    public static final String PUBLIC_TOILET_GET_DATA_JSON = "/services/ZWFWSJGLJ_DB_92/gcsvh/"
            + DA_PENG_TOKEN_PLACEHOLDER + "/getDataJson";

    /**
     * 医院信息获取数据接口
     */
    public static final String HOSPITAL_GET_DATA_JSON = "/services/ZWFWSJGLJ_DB_88/yybgw/" + DA_PENG_TOKEN_PLACEHOLDER
            + "/getDataJson";

    /**
     * 娱乐场所信息获取数据接口
     */
    public static final String ENTERTAINMENT_VENUE_GET_DATA_JSON = "/services/ZWFWSJGLJ_DB_86/ylcslbz/"
            + DA_PENG_TOKEN_PLACEHOLDER + "/getDataJson";

    /**
     * 停车场信息获取数据接口
     */
    public static final String PARKING_LOT_GET_DATA_JSON = "/services/ZWFWSJGLJ_DB_75/tcccej/"
            + DA_PENG_TOKEN_PLACEHOLDER + "/getDataJson";

    /**
     * 古村落信息获取数据接口
     */
    public static final String ANCIENT_VILLAGE_GET_DATA_JSON = "/services/ZWFWSJGLJ_DB_58/gclsdi/"
            + DA_PENG_TOKEN_PLACEHOLDER + "/getDataJson";

    /**
     * 古村落2信息获取数据接口
     */
    public static final String ANCIENT_VILLAGE2_GET_DATA_JSON = "/services/ZWFWSJGLJ_DB_139/gcl2/"
            + DA_PENG_TOKEN_PLACEHOLDER + "/getDataJson";

    /**
     * 景区信息获取数据接口
     */
    public static final String SCENIC_AREA_GET_DATA_JSON = "/services/ZWFWSJGLJ_DB_49/jqwbq/"
            + DA_PENG_TOKEN_PLACEHOLDER + "/getDataJson";

    /**
     * 旅行社和网点信息获取数据接口
     */
    public static final String TRAVEL_AGENCY_GET_DATA_JSON = "/services/ZWFWSJGLJ_DB_50/lxshwdame/"
            + DA_PENG_TOKEN_PLACEHOLDER + "/getDataJson";
}
