package com.deepinnet.localdata.integration.model.output;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Description: 船舶数据上报信息响应DTO
 * Date: 2025/8/27
 * Author: qoder
 */
@Data
public class BoatDataReportResponseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 获取数据Json响应
     */
    @JsonProperty("getDataJsonResponse")
    private GetDataJsonResponse getDataJsonResponse;

    @Data
    public static class GetDataJsonResponse implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 返回状态信息（注意：这里是字符串，不是对象）
         */
        @JsonProperty("return")
        private String returnInfo;

        /**
         * 数据内容
         */
        private DataInfo data;
    }

    @Data
    public static class ReturnInfo implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 状态码：1：成功，-1：失败，401：token有误
         */
        private String code;

        /**
         * 状态信息
         */
        private String message;

        /**
         * 是否支持搜索
         */
        @JsonProperty("hasSearch")
        private Boolean hasSearch;

        /**
         * 真实数据类型
         */
        @JsonProperty("realDataType")
        private Integer realDataType;

        /**
         * 错误标识
         */
        @JsonProperty("errorFlag")
        private Integer errorFlag;

        /**
         * 是否为文件
         */
        @JsonProperty("isFile")
        private Boolean isFile;
    }

    @Data
    public static class DataInfo implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 总记录数
         */
        private Integer totalRecords;

        /**
         * 错误标识，0表示正常，1表示错误
         */
        private Integer errorFlag;

        /**
         * 数据结果列表
         */
        private List<DataReportRecord> result;
    }

    @Data
    public static class DataReportRecord implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 上报记录ID
         */
        private String reportId;

        /**
         * 船舶名称
         */
        private String boatName;

        /**
         * 船舶编号
         */
        private String boatNumber;

        /**
         * 船舶类型
         */
        private String boatType;

        /**
         * 船舶所有者
         */
        private String boatOwner;

        /**
         * 船舶长度
         */
        private String boatLength;

        /**
         * 船舶宽度
         */
        private String boatWidth;

        /**
         * 船舶吨位
         */
        private String boatTonnage;

        /**
         * 船舶建造年份
         */
        private String buildYear;

        /**
         * 船舶建造地点
         */
        private String buildLocation;

        /**
         * 船舶注册港口
         */
        private String registrationPort;

        /**
         * 上报时间
         */
        private String reportTime;

        /**
         * 上报类型
         */
        private String reportType;

        /**
         * 上报状态
         */
        private String reportStatus;

        /**
         * 船舶位置经度
         */
        private String longitude;

        /**
         * 船舶位置纬度
         */
        private String latitude;

        /**
         * 船舶当前速度
         */
        private String currentSpeed;

        /**
         * 船舶航向
         */
        private String heading;

        /**
         * 船舶目的港
         */
        private String destinationPort;

        /**
         * 预计到达时间
         */
        private String estimatedArrivalTime;

        /**
         * 货物信息
         */
        private String cargoInfo;

        /**
         * 船员数量
         */
        private String crewCount;

        /**
         * 旅客数量
         */
        private String passengerCount;

        /**
         * 联系人
         */
        private String contactPerson;

        /**
         * 联系电话
         */
        private String contactPhone;

        /**
         * 联系邮箱
         */
        private String contactEmail;

        /**
         * 创建时间
         */
        private String createTime;

        /**
         * 更新时间
         */
        private String updateTime;

        /**
         * 数据来源
         */
        private String dataSource;

        /**
         * 备注
         */
        private String remark;
    }
}