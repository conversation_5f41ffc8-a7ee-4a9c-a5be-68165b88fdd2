package com.deepinnet.localdata.integration.model.input;

import lombok.Data;

/**
 * 大鹏Token请求DTO
 * Date: 2025/9/1
 * Author: qoder
 */
@Data
public class DaPengTokenRequestDTO {

    /**
     * 客户端ID（根据大鹏接口文档填写）
     */
    private String clientId;

    /**
     * 客户端密钥（根据大鹏接口文档填写）
     */
    private String clientSecret;

    /**
     * 授权类型（通常为client_credentials）
     */
    private String grantType = "client_credentials";

    /**
     * 用户名（如果需要）
     */
    private String username;

    /**
     * 用户名（大鹏接口专用字段）
     */
    private String userName;

    /**
     * 密码（如果需要）
     */
    private String password;

    /**
     * 密码MD5加密（大鹏接口专用）
     */
    private String pwd;

    /**
     * IP地址（可选，可为空）
     */
    private String ip = "";

    /**
     * Token有效时间（秒），默认86400秒（24小时）
     */
    private String time = "86400";

    /**
     * 其他可能需要的参数
     */
    private String scope;
}