package com.deepinnet.localdata.integration.service;

import cn.hutool.core.util.StrUtil;
import com.deepinnet.digitaltwin.common.util.JsonUtil;
import com.deepinnet.localdata.integration.http.HttpResult;
import com.deepinnet.localdata.integration.http.HttpUtils;
import com.deepinnet.localdata.integration.http.HttpRequestParamNew;
import com.deepinnet.localdata.integration.http.HttpRequestParamUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 大鹏HTTP请求服务
 * Date: 2024/8/29
 * Author: lijunheng
 */
@Service
@Slf4j
public class DaPengHttpRequestService {

    @Resource
    private DaPengApiConnectionInfo connectInfo;

    @Resource
    private DaPengTokenService tokenService;

    /**
     * 发送HTTP请求
     *
     * @param httpRequestParam HTTP请求参数
     * @param clazz            返回结果类型
     * @param <T>              返回结果泛型
     * @return HTTP响应结果
     */
    public <T> T httpRequest(HttpRequestParamNew httpRequestParam, Class<T> clazz) {
        log.info("开始发送大鹏接口请求: {}", httpRequestParam);

        String requestParam = HttpRequestParamUtils.convertMultiValuedMapToJson(httpRequestParam.getRequestParam());
        String action = httpRequestParam.getAction();
        String url = httpRequestParam.getAddress();

        // 构建完整URL（不再进行Token替换，因为已经在Client中处理过了）
        if (StrUtil.isNotBlank(action)) {
            url = httpRequestParam.getAddress() + action;
        }

        log.info("发送大鹏接口请求: {}", url);

        HttpResult response = HttpUtils.doGet(url,
                httpRequestParam.getRequestParam(),
                httpRequestParam.getRequestHeader());

        try {
            if (response.getStatusCode() == 200) {
                log.info("大鹏接口请求成功，开始解析响应数据");
                String body = response.getBody();
                log.debug("响应体内容: {}", body);
                return JsonUtil.parseJson(body, clazz);
            } else {
                log.warn("调用接口非200返回，请求:{} 响应:{}", httpRequestParam, response);
                return null;
            }
        } catch (Exception e) {
            log.error("解析响应数据异常，请求:{}", httpRequestParam, e);
            return null;
        }
    }
}