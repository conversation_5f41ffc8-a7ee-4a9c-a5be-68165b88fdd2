package com.deepinnet.localdata.integration.model.output;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Description: 船舶状态信息响应DTO
 * Date: 2025/8/27
 * Author: qoder
 */
@Data
public class BoatStatusResponseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 获取数据Json响应
     */
    @JsonProperty("getDataJsonResponse")
    private GetDataJsonResponse getDataJsonResponse;

    @Data
    public static class GetDataJsonResponse implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 返回状态信息（注意：这里是字符串，不是对象）
         */
        @JsonProperty("return")
        private String returnInfo;

        /**
         * 数据内容
         */
        private DataInfo data;
    }

    @Data
    public static class ReturnInfo implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 状态码：1：成功，-1：失败，401：token有误
         */
        private String code;

        /**
         * 状态信息
         */
        private String message;

        /**
         * 是否支持搜索
         */
        @JsonProperty("hasSearch")
        private Boolean hasSearch;

        /**
         * 真实数据类型
         */
        @JsonProperty("realDataType")
        private Integer realDataType;

        /**
         * 错误标识
         */
        @JsonProperty("errorFlag")
        private Integer errorFlag;

        /**
         * 是否为文件
         */
        @JsonProperty("isFile")
        private Boolean isFile;
    }

    @Data
    public static class DataInfo implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 总记录数
         */
        private Integer totalRecords;

        /**
         * 错误标识，0表示正常，1表示错误
         */
        private Integer errorFlag;

        /**
         * 数据结果列表
         */
        private List<StatusRecord> result;
    }

    @Data
    public static class StatusRecord implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 状态记录ID
         */
        private String statusId;

        /**
         * 船舶名称
         */
        private String boatName;

        /**
         * 船舶编号
         */
        private String boatNumber;

        /**
         * 船舶类型
         */
        private String boatType;

        /**
         * 船舶状态
         */
        private String boatStatus;

        /**
         * 状态描述
         */
        private String statusDescription;

        /**
         * 状态时间
         */
        private String statusTime;

        /**
         * 船舶位置经度
         */
        private String longitude;

        /**
         * 船舶位置纬度
         */
        private String latitude;

        /**
         * 当前速度（节）
         */
        private String currentSpeed;

        /**
         * 航向角度
         */
        private String heading;

        /**
         * 水深
         */
        private String waterDepth;

        /**
         * 船舶吃水深度
         */
        private String draft;

        /**
         * 发动机状态
         */
        private String engineStatus;

        /**
         * 发动机转速
         */
        private String engineRpm;

        /**
         * 燃料余量
         */
        private String fuelLevel;

        /**
         * 燃料消耗率
         */
        private String fuelConsumption;

        /**
         * 淡水余量
         */
        private String freshWaterLevel;

        /**
         * 船员在船状态
         */
        private String crewOnBoard;

        /**
         * 货物装载状态
         */
        private String cargoStatus;

        /**
         * 货物重量
         */
        private String cargoWeight;

        /**
         * 通信设备状态
         */
        private String communicationStatus;

        /**
         * 导航设备状态
         */
        private String navigationStatus;

        /**
         * 救生设备状态
         */
        private String lifeSavingEquipmentStatus;

        /**
         * 消防设备状态
         */
        private String fireEquipmentStatus;

        /**
         * 天气状况
         */
        private String weatherCondition;

        /**
         * 海况等级
         */
        private String seaCondition;

        /**
         * 风速
         */
        private String windSpeed;

        /**
         * 风向
         */
        private String windDirection;

        /**
         * 船舶运行模式
         */
        private String operationMode;

        /**
         * 最后更新时间
         */
        private String lastUpdateTime;

        /**
         * 数据来源
         */
        private String dataSource;

        /**
         * 船舶操作员
         */
        private String operator;

        /**
         * 船长姓名
         */
        private String captainName;

        /**
         * 紧急联系人
         */
        private String emergencyContact;

        /**
         * 紧急联系电话
         */
        private String emergencyPhone;

        /**
         * 船舶证书状态
         */
        private String certificateStatus;

        /**
         * 保险状态
         */
        private String insuranceStatus;

        /**
         * 维护状态
         */
        private String maintenanceStatus;

        /**
         * 下次维护时间
         */
        private String nextMaintenanceTime;

        /**
         * 备注
         */
        private String remark;
    }
}