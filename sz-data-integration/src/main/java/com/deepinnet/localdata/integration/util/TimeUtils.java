package com.deepinnet.localdata.integration.util;

import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

/**
 * 时间工具类
 *
 * <AUTHOR>
 * @name TimeUtils
 * @date 2024-11-06 18:41
 */

public class TimeUtils {

    /**
     * 判断给定的Date对象是否在当前时间的一小时内。
     *
     * @param date 给定的Date对象
     * @return 如果给定时间与当前时间差在一小时以内则返回true, 否则返回false
     */
    public static boolean isWithinOneHour(Date date) {
        if (date == null) return false;

        Instant nowInstant = Instant.now();
        LocalDateTime now = LocalDateTime.ofInstant(nowInstant, ZoneId.systemDefault());

        Instant dateInstant = date.toInstant();
        LocalDateTime dateTime = LocalDateTime.ofInstant(dateInstant, ZoneId.systemDefault());

        Duration duration = Duration.between(dateTime, now);
        long minutes = Math.abs(duration.toMinutes());
        return minutes <= 60;
    }

}
