package com.deepinnet.localdata.integration.service;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Description: 大鹏API连接信息配置
 * Date: 2025/8/26
 * Author: qoder
 */
@Data
@Component
@ConfigurationProperties(prefix = "out-api.da-peng")
public class DaPengApiConnectionInfo {

    /**
     * 服务器URL（仅包含协议、主机、端口）- 用于数据接口
     */
    private String serverUrl;

    /**
     * Token服务器URL（仅包含协议、主机、端口）- 用于获取Token
     */
    private String tokenServerUrl;

    /**
     * 基础路径（预留字段，当前为空）
     */
    private String basePath;

    /**
     * 动态访问令牌（用于用户身份认证，可选）
     * 注意：URL路径中的长串是服务标识符，与此token不同
     */
    private String token;

    /**
     * OAuth2客户端ID（用于动态获取Token）
     */
    private String clientId;

    /**
     * OAuth2客户端密钥（用于动态获取Token）
     */
    private String clientSecret;

    /**
     * 大鹏系统用户名（用于获取Token）
     */
    private String userName;

    /**
     * 大鹏系统密码（用于获取Token）
     */
    private String password;

    /**
     * 大鹏系统密码MD5加密值（用于获取Token的pwd参数）
     */
    private String passwordMd5;

    /**
     * Token有效时间（秒），默认86400（24小时）
     */
    private String tokenTime = "86400";

    /**
     * 是否启用模拟数据模式（开发测试用）
     */
    private boolean mockEnabled = false;

    /**
     * 获取完整的服务地址（数据接口用）
     *
     * @return 完整的服务地址
     */
    public String getFullServerUrl() {
        if (serverUrl == null) {
            return null;
        }

        if (basePath == null || basePath.isEmpty()) {
            return serverUrl;
        }

        String url = serverUrl;
        if (!url.endsWith("/")) {
            url += "/";
        }

        String path = basePath;
        if (path.startsWith("/")) {
            path = path.substring(1);
        }

        return url + path;
    }

    /**
     * 获取完整的Token服务器地址
     *
     * @return 完整的Token服务器地址
     */
    public String getFullTokenServerUrl() {
        if (tokenServerUrl == null) {
            // 如果没有配置Token服务器地址，则使用数据服务器地址
            return getFullServerUrl();
        }

        return tokenServerUrl;
    }
}
