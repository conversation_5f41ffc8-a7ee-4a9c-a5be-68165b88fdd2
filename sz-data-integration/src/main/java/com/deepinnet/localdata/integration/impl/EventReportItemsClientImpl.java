package com.deepinnet.localdata.integration.impl;

import com.deepinnet.localdata.integration.EventReportItemsClient;
import com.deepinnet.localdata.integration.model.output.EventReportItemsResponseDTO;

import java.util.ArrayList;
import java.util.List;

/**
 * 事项清单客户端实现
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-28
 */
public class EventReportItemsClientImpl implements EventReportItemsClient {

    @Override
    public EventReportItemsResponseDTO getItems() {
        System.out.println("开始获取事项清单");

        try {
            // 根据接口文档构建响应数据
            EventReportItemsResponseDTO response = new EventReportItemsResponseDTO();

            // 构建自定义数据
            EventReportItemsResponseDTO.Custom custom = new EventReportItemsResponseDTO.Custom();
            List<EventReportItemsResponseDTO.Item> items = new ArrayList<>();

            // 添加示例数据（根据接口文档中的示例）
            EventReportItemsResponseDTO.Item item = new EventReportItemsResponseDTO.Item();
            item.setSecondcatalog("二级事项目录编码");
            item.setSecondcatalogname("二级事项目录名称");
            item.setItemname("事项清单名称");
            item.setFirstcatalog("一级事项目录编码");
            item.setItemcode("事项清单编码");
            item.setFirstcatalogname("一级事项目录名称");

            items.add(item);
            custom.setItems(items);
            response.setCustom(custom);

            // 构建状态信息
            EventReportItemsResponseDTO.Status status = new EventReportItemsResponseDTO.Status();
            status.setCode(1);
            status.setText("请求成功");
            response.setStatus(status);

            System.out.println("成功获取事项清单，共" + items.size() + "条记录");
            return response;

        } catch (Exception e) {
            System.err.println("获取事项清单失败: " + e.getMessage());

            // 构建失败响应
            EventReportItemsResponseDTO response = new EventReportItemsResponseDTO();
            EventReportItemsResponseDTO.Status status = new EventReportItemsResponseDTO.Status();
            status.setCode(0);
            status.setText("获取事项清单失败");
            response.setStatus(status);

            return response;
        }
    }
}