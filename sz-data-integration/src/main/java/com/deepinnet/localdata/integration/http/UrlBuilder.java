package com.deepinnet.localdata.integration.http;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;


/**
 * Description:
 * Date: 2024/9/3
 * Author: lijunheng
 */
public class UrlBuilder {
    private final StringBuilder builder;

    public UrlBuilder() {
        this.builder = new StringBuilder();
    }

    public static UrlBuilder create() {
        return new UrlBuilder();
    }

    // 添加参数的方法，自动处理编码
    public UrlBuilder addParam(String key, String value) {
        if (builder.length() == 0) {
            builder.append("?");
        } else {
            builder.append("&");
        }
        try {
            builder.append(URLEncoder.encode(key, StandardCharsets.UTF_8.toString()))
                    .append("=")
                    .append(URLEncoder.encode(value, StandardCharsets.UTF_8.toString()));
        } catch (UnsupportedEncodingException e) {
            // 处理编码异常
            throw new RuntimeException("Error encoding URL parameter", e);
        }
        return this;
    }

    public String build() {
        return builder.toString();
    }
}

