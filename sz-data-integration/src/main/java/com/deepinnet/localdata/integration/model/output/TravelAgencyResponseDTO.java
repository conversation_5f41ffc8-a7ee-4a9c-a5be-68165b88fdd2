package com.deepinnet.localdata.integration.model.output;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Description: 旅行社和网点信息响应DTO
 * Date: 2025/8/27
 * Author: qoder
 */
@Data
public class TravelAgencyResponseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 获取数据Json响应
     */
    @JsonProperty("getDataJsonResponse")
    private GetDataJsonResponse getDataJsonResponse;

    @Data
    public static class GetDataJsonResponse implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 返回状态信息（注意：这里是字符串，不是对象）
         */
        @JsonProperty("return")
        private String returnInfo;

        /**
         * 数据内容
         */
        private DataInfo data;
    }

    @Data
    public static class ReturnInfo implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 状态码：1：成功，-1：失败，401：token有误
         */
        private String code;

        /**
         * 状态信息
         */
        private String message;

        /**
         * 是否支持搜索
         */
        @JsonProperty("hasSearch")
        private Boolean hasSearch;

        /**
         * 真实数据类型
         */
        @JsonProperty("realDataType")
        private Integer realDataType;

        /**
         * 错误标识
         */
        @JsonProperty("errorFlag")
        private Integer errorFlag;

        /**
         * 是否为文件
         */
        @JsonProperty("isFile")
        private Boolean isFile;
    }

    @Data
    public static class DataInfo implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 总记录数
         */
        private Integer totalRecords;

        /**
         * 错误标识，0表示正常，1表示错误
         */
        private Integer errorFlag;

        /**
         * 数据结果列表
         */
        private List<TravelAgencyRecord> result;
    }

    @Data
    public static class TravelAgencyRecord implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 旅行社ID
         */
        private String travelAgencyId;

        /**
         * 旅行社名称
         */
        private String travelAgencyName;

        /**
         * 旅行社编号
         */
        private String travelAgencyCode;

        /**
         * 旅行社类型（国际社、国内社、门市部等）
         */
        private String travelAgencyType;

        /**
         * 旅行社等级
         */
        private String travelAgencyLevel;

        /**
         * 经营范围
         */
        private String businessScope;

        /**
         * 统一社会信用代码
         */
        private String unifiedSocialCreditCode;

        /**
         * 工商注册号
         */
        private String businessRegistrationNumber;

        /**
         * 组织机构代码
         */
        private String organizationCode;

        /**
         * 税务登记号
         */
        private String taxRegistrationNumber;

        /**
         * 法人代表
         */
        private String legalRepresentative;

        /**
         * 法人代表身份证号
         */
        private String legalIdNumber;

        /**
         * 总经理
         */
        private String generalManager;

        /**
         * 成立日期
         */
        private String establishmentDate;

        /**
         * 注册资本（万元）
         */
        private String registeredCapital;

        /**
         * 实收资本（万元）
         */
        private String paidCapital;

        /**
         * 企业性质
         */
        private String enterpriseNature;

        /**
         * 所在省份
         */
        private String province;

        /**
         * 所在城市
         */
        private String city;

        /**
         * 所在区县
         */
        private String district;

        /**
         * 所在街道/乡镇
         */
        private String street;

        /**
         * 详细地址
         */
        private String detailedAddress;

        /**
         * 邮政编码
         */
        private String postalCode;

        /**
         * 经度
         */
        private String longitude;

        /**
         * 纬度
         */
        private String latitude;

        /**
         * 联系电话
         */
        private String contactPhone;

        /**
         * 传真
         */
        private String fax;

        /**
         * 电子邮箱
         */
        private String email;

        /**
         * 官方网站
         */
        private String officialWebsite;

        /**
         * 微信公众号
         */
        private String wechatAccount;

        /**
         * 旅行社业务经营许可证编号
         */
        private String businessLicenseNumber;

        /**
         * 许可证颁发日期
         */
        private String licenseIssueDate;

        /**
         * 许可证有效期
         */
        private String licenseValidityPeriod;

        /**
         * 许可证颁发机关
         */
        private String licenseIssuingAuthority;

        /**
         * 经营状态
         */
        private String businessStatus;

        /**
         * 年检状态
         */
        private String annualInspectionStatus;

        /**
         * 最后年检日期
         */
        private String lastInspectionDate;

        /**
         * 下次年检日期
         */
        private String nextInspectionDate;

        /**
         * 质量保证金（万元）
         */
        private String qualityDeposit;

        /**
         * 保证金缴存银行
         */
        private String depositBank;

        /**
         * 保证金账号
         */
        private String depositAccountNumber;

        /**
         * 保险公司
         */
        private String insuranceCompany;

        /**
         * 保险单号
         */
        private String insurancePolicyNumber;

        /**
         * 保险金额（万元）
         */
        private String insuranceAmount;

        /**
         * 保险有效期
         */
        private String insuranceValidityPeriod;

        /**
         * 从业人员总数
         */
        private String totalEmployees;

        /**
         * 导游人数
         */
        private String guideCount;

        /**
         * 领队人数
         */
        private String tourLeaderCount;

        /**
         * 计调人员人数
         */
        private String plannerCount;

        /**
         * 销售人员人数
         */
        private String salesCount;

        /**
         * 财务人员人数
         */
        private String financeCount;

        /**
         * 总门店数量
         */
        private String totalOutletCount;

        /**
         * 直营门店数量
         */
        private String directOutletCount;

        /**
         * 加盟门店数量
         */
        private String franchiseOutletCount;

        /**
         * 网点分布城市数
         */
        private String outletCityCount;

        /**
         * 主要业务类型
         */
        private String mainBusinessTypes;

        /**
         * 出境游业务
         */
        private String outboundTourism;

        /**
         * 入境游业务
         */
        private String inboundTourism;

        /**
         * 国内游业务
         */
        private String domesticTourism;

        /**
         * 商务旅行业务
         */
        private String businessTravel;

        /**
         * 会议会展业务
         */
        private String miceService;

        /**
         * 在线旅游平台
         */
        private String onlinePlatform;

        /**
         * 年营业收入（万元）
         */
        private String annualRevenue;

        /**
         * 年接待游客量（万人次）
         */
        private String annualTouristCount;

        /**
         * 年组织出境游人数（万人次）
         */
        private String annualOutboundTourists;

        /**
         * 年组织国内游人数（万人次）
         */
        private String annualDomesticTourists;

        /**
         * 年接待入境游客数（万人次）
         */
        private String annualInboundTourists;

        /**
         * 主要目的地国家/地区
         */
        private String mainDestinations;

        /**
         * 合作航空公司
         */
        private String partnerAirlines;

        /**
         * 合作酒店集团
         */
        private String partnerHotels;

        /**
         * 合作景区
         */
        private String partnerAttractions;

        /**
         * 服务质量等级
         */
        private String serviceQualityLevel;

        /**
         * 客户满意度（%）
         */
        private String customerSatisfaction;

        /**
         * 投诉处理满意度（%）
         */
        private String complaintSatisfaction;

        /**
         * 年投诉量
         */
        private String annualComplaints;

        /**
         * 重大安全事故次数
         */
        private String majorSafetyIncidents;

        /**
         * 获得荣誉
         */
        private String honors;

        /**
         * 行业认证
         */
        private String industryCertifications;

        /**
         * 获奖情况
         */
        private String awards;

        /**
         * 行业排名
         */
        private String industryRanking;

        /**
         * 品牌价值（万元）
         */
        private String brandValue;

        /**
         * 信用等级
         */
        private String creditRating;

        /**
         * 诚信记录
         */
        private String integrityRecord;

        /**
         * 违法违规记录
         */
        private String violationRecord;

        /**
         * 行政处罚记录
         */
        private String penaltyRecord;

        /**
         * 监管部门
         */
        private String regulatoryDepartment;

        /**
         * 监管措施
         */
        private String regulatoryMeasures;

        /**
         * 检查频次
         */
        private String inspectionFrequency;

        /**
         * 合规状态
         */
        private String complianceStatus;

        /**
         * 发展规划
         */
        private String developmentPlan;

        /**
         * 投资计划（万元）
         */
        private String investmentPlan;

        /**
         * 扩张计划
         */
        private String expansionPlan;

        /**
         * 数字化转型情况
         */
        private String digitalTransformation;

        /**
         * 疫情影响评估
         */
        private String pandemicImpact;

        /**
         * 恢复情况
         */
        private String recoveryStatus;

        /**
         * 发展前景
         */
        private String developmentProspects;

        /**
         * 存在问题
         */
        private String existingProblems;

        /**
         * 改进措施
         */
        private String improvementMeasures;

        /**
         * 备注
         */
        private String remarks;

        /**
         * 数据更新时间
         */
        private String updateTime;

        /**
         * 数据来源
         */
        private String dataSource;

        /**
         * 数据状态
         */
        private String dataStatus;

        /**
         * 创建时间
         */
        private String createTime;

        /**
         * 录入人员
         */
        private String dataEntryPerson;
    }
}