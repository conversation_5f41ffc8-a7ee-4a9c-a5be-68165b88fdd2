package com.deepinnet.localdata;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.util.Arrays;

/**
 * Description:
 * Date: 2024/8/29
 * Author: lijunheng
 */
@SpringBootApplication(scanBasePackages = { "com.deepinnet.localdata.integration",
        "com.deepinnet.localdata.controller" }, exclude = {
                org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration.class,
                org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration.class,
                com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.class,
        })
@EnableConfigurationProperties
@EnableScheduling
public class ApplicationStart {

    public static void main(String[] args) {
        // 设置默认激活的配置文件
        System.setProperty("spring.profiles.active", "local");
        SpringApplication.run(ApplicationStart.class, args);
    }
}