package com.deepinnet.localdata.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

/**
 * 定时任务配置类
 * 配置定时任务线程池
 */
@Configuration
public class ScheduledTaskConfig {

    /**
     * 配置定时任务线程池
     * @return TaskScheduler
     */
    @Bean
    public TaskScheduler taskScheduler() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        scheduler.setPoolSize(10); // 线程池大小
        scheduler.setThreadNamePrefix("boat-data-scheduled-task-"); // 线程名称前缀
        scheduler.setWaitForTasksToCompleteOnShutdown(true); // 等待任务完成后再关闭
        scheduler.setAwaitTerminationSeconds(60); // 等待60秒
        return scheduler;
    }
}