package com.deepinnet.localdata.task;

import com.deepinnet.localdata.integration.BoatDataReportClient;
import com.deepinnet.localdata.integration.model.output.BoatDataReportResponseDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 船舶数据定时获取任务
 * 每秒获取船舶上报信息并写入日志文件
 */
@Slf4j
@Component
public class BoatDataScheduledTask {

    @Resource
    private BoatDataReportClient boatDataReportClient;

    /**
     * 每秒执行一次，获取船舶上报信息
     * cron表达式：* * * * * ? 表示每秒执行
     */
    @Scheduled(cron = "* * * * * ?")
    public void fetchBoatDataReport() {
        try {
            log.info("开始获取船舶上报信息 - {}", 
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            
            // 获取船舶数据上报信息
            BoatDataReportResponseDTO response = boatDataReportClient.getBoatDataReportData();
            
            if (response != null && response.getGetDataJsonResponse() != null) {
                String returnInfo = response.getGetDataJsonResponse().getReturnInfo();
                BoatDataReportResponseDTO.DataInfo dataInfo = response.getGetDataJsonResponse().getData();
                
                log.info("船舶上报信息获取成功:");
                log.info("  返回信息: {}", returnInfo);
                
                if (dataInfo != null) {
                    log.info("  总记录数: {}", dataInfo.getTotalRecords());
                    log.info("  错误标识: {}", dataInfo.getErrorFlag());
                    log.info("  结果数量: {}", dataInfo.getResult() != null ? dataInfo.getResult().size() : 0);
                    
                    // 记录部分数据详情
                    if (dataInfo.getResult() != null && !dataInfo.getResult().isEmpty()) {
                        log.info("  部分数据详情:");
                        // 只记录前3条数据以避免日志过大
                        int count = 0;
                        for (BoatDataReportResponseDTO.DataReportRecord record : dataInfo.getResult()) {
                            if (count >= 3) break;
                            log.info("    记录{}: 船名={}, 船号={}, 状态={}, 时间={}", 
                                count + 1, 
                                record.getBoatName(), 
                                record.getBoatNumber(), 
                                record.getReportStatus(), 
                                record.getCreateTime());
                            count++;
                        }
                        if (dataInfo.getResult().size() > 3) {
                            log.info("    ... 还有{}条数据", dataInfo.getResult().size() - 3);
                        }
                    }
                }
            } else {
                log.warn("船舶上报信息获取失败: 返回数据为空");
            }
            
            log.info("船舶上报信息获取完成 - {}", 
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                
        } catch (Exception e) {
            log.error("获取船舶上报信息时发生异常 - {}", 
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")), e);
        }
    }
}