package com.deepinnet.localdata.controller;

import com.deepinnet.localdata.integration.TravelAgencyClient;
import com.deepinnet.localdata.integration.model.input.TravelAgencyQueryDTO;
import com.deepinnet.localdata.integration.model.output.TravelAgencyResponseDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * Description: 旅行社和网点信息接口测试控制器
 * Date: 2025/8/27
 * Author: qoder
 */
@Api(tags = "旅行社和网点信息接口")
@RestController
@RequestMapping("/travel-agency")
@Slf4j
public class TravelAgencyController {

    @Resource
    private TravelAgencyClient travelAgencyClient;

    /**
     * 获取旅行社和网点信息（默认参数）
     */
    @ApiOperation(value = "获取旅行社和网点信息（默认）", notes = "使用默认参数获取旅行社和网点信息JSON数据")
    @GetMapping("/data")
    public TravelAgencyResponseDTO getData() {
        try {
            log.info("开始调用大鹏接口获取旅行社和网点信息（默认参数）");

            TravelAgencyResponseDTO response = travelAgencyClient.getTravelAgencyData();

            log.info("成功获取旅行社和网点信息");
            return response;

        } catch (Exception e) {
            log.error("获取旅行社和网点信息失败", e);
            throw e;
        }
    }

    /**
     * 获取旅行社和网点信息（带参数）
     */
    @ApiOperation(value = "获取旅行社和网点信息（带参数）", notes = "使用指定参数获取旅行社和网点信息JSON数据")
    @GetMapping("/data/paged")
    public TravelAgencyResponseDTO getDataPaged(
            @RequestParam(defaultValue = "1") @ApiParam(value = "页号，默认为1") String pageNo,
            @RequestParam(defaultValue = "10") @ApiParam(value = "页面大小，默认为10，最大100") String pageSize,
            @RequestParam(defaultValue = "") @ApiParam(value = "搜索条件JSON字符串，为空传\"\"") String search,
            @RequestParam(required = false) @ApiParam(value = "token参数（可选）") String token) {
        try {
            log.info("开始调用大鹏接口获取旅行社和网点信息，参数: pageNo={}, pageSize={}, search={}, token={}",
                    pageNo, pageSize, search, token);

            TravelAgencyQueryDTO queryDTO = new TravelAgencyQueryDTO();
            queryDTO.setPageNo(pageNo);
            queryDTO.setPageSize(pageSize);
            queryDTO.setSearch(search);
            queryDTO.setToken(token);

            TravelAgencyResponseDTO response = travelAgencyClient.getTravelAgencyData(queryDTO);

            log.info("成功获取旅行社和网点信息");
            return response;

        } catch (Exception e) {
            log.error("获取旅行社和网点信息失败，参数: pageNo={}, pageSize={}, search={}", pageNo, pageSize, search, e);
            throw e;
        }
    }

    /**
     * 获取旅行社和网点信息（POST方式）
     */
    @ApiOperation(value = "获取旅行社和网点信息（POST）", notes = "使用POST方式提交查询参数获取旅行社和网点信息JSON数据")
    @PostMapping("/data")
    public TravelAgencyResponseDTO getDataByPost(
            @RequestBody @ApiParam(value = "查询参数", required = true) TravelAgencyQueryDTO queryDTO) {
        try {
            log.info("开始调用大鹏接口获取旅行社和网点信息（POST），参数: {}", queryDTO);

            TravelAgencyResponseDTO response = travelAgencyClient.getTravelAgencyData(queryDTO);

            log.info("成功获取旅行社和网点信息");
            return response;

        } catch (Exception e) {
            log.error("获取旅行社和网点信息失败，参数: {}", queryDTO, e);
            throw e;
        }
    }

    /**
     * 测试旅行社信息接口连通性
     */
    @ApiOperation(value = "测试旅行社信息接口", notes = "测试旅行社信息接口的连通性和基本功能")
    @GetMapping("/test")
    public Map<String, Object> testConnection() {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("开始测试大鹏旅行社信息接口连通性");

            // 测试基本连接
            long startTime = System.currentTimeMillis();
            TravelAgencyResponseDTO response = travelAgencyClient.getTravelAgencyData();
            long endTime = System.currentTimeMillis();

            result.put("success", true);
            result.put("response_time_ms", endTime - startTime);
            result.put("has_response", response != null);

            if (response != null && response.getGetDataJsonResponse() != null) {
                // 注意：returnInfo是字符串类型，不是ReturnInfo对象
                String returnInfoStr = response.getGetDataJsonResponse().getReturnInfo();
                if (returnInfoStr != null) {
                    result.put("return_info", returnInfoStr);
                }

                TravelAgencyResponseDTO.DataInfo dataInfo = response.getGetDataJsonResponse().getData();
                if (dataInfo != null) {
                    result.put("total_records", dataInfo.getTotalRecords());
                    result.put("error_flag", dataInfo.getErrorFlag());
                    result.put("result_count", dataInfo.getResult() != null ? dataInfo.getResult().size() : 0);
                }
            }

            log.info("大鹏旅行社信息接口测试成功");

        } catch (Exception e) {
            log.error("大鹏旅行社信息接口测试失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }

        return result;
    }
}