package com.deepinnet.localdata.controller;

import com.alibaba.fastjson2.JSON;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.localdata.integration.CameraClient;
import com.deepinnet.localdata.integration.RegionTrafficFlowClient;
import com.deepinnet.localdata.integration.listener.DaHuaLoginClient;
import com.deepinnet.localdata.integration.model.input.RealMonitorQueryDTO;
import com.deepinnet.localdata.integration.model.outsidebean.ClientTypeEnum;
import com.deepinnet.localdata.integration.model.outsidebean.VehiclePassQuery;
import com.deepinnet.localdata.integration.model.outsidebean.VehiclePassResponse;
import com.deepinnet.localdata.integration.service.DaHuaApiConnectionInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2024-11-14
 */
@Api
@RestController
@Slf4j
public class DahuaClientTestController {

    @Resource
    private DaHuaLoginClient daHuaLoginClient;

    @Resource
    private DaHuaApiConnectionInfo connectUserInfo;

    @Resource
    private CameraClient cameraClient;

    @Resource
    private RegionTrafficFlowClient regionTrafficFlowClient;

    @GetMapping("/test/dahua/token")
    private String token() {
        try {
            String token = this.daHuaLoginClient.token(this.connectUserInfo.getUsername(), this.connectUserInfo.getPassword(), ClientTypeEnum.WINPC);
            log.info("get dahua token:{}", token);
            return "DAHUA_TOKEN:" + token;
        } catch (Exception e) {
            log.error("获取token异常", e);
            return e.getMessage();
        }
    }


    @ApiOperation("通过设备编码获取实时视频地址")
    @PostMapping("/realtime/url")
    public Result<String> getRealMonitorUrl(@RequestBody RealMonitorQueryDTO queryDTO) {
        try {
            return Result.success(cameraClient.getRealtimeCameraVideoStreamUrl(queryDTO));
        } catch (Exception e) {
            log.error("获取视频地址错误", e);
        }
        return Result.fail("GET_REAL_MONITOR_URL_ERROR","获取视频地址错误");
    }


    @ApiOperation("查询过车记录")
    @PostMapping("/car/pass")
    public Result<String> queryCarPass(@RequestBody VehiclePassQuery query) {
        try {
            CommonPage<VehiclePassResponse> result = regionTrafficFlowClient.getVehiclePass(query, 1, 1000);
            return Result.success(JSON.toJSONString(result));
        } catch (Exception e) {
            log.error("查询过车记录失败", e);
            return Result.fail("QUERY_CAR_PASS_ERROR",e.getMessage());
        }
    }

}
