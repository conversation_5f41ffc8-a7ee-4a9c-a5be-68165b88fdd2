package com.deepinnet.localdata.controller;

import com.deepinnet.localdata.integration.AirspaceClient;
import com.deepinnet.localdata.integration.model.input.NoFlyZoneQueryDTO;
import com.deepinnet.localdata.integration.model.output.NoFlyZoneResponseDTO;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2025-03-14
 */
@Api
@RestController
@Slf4j
public class AirSpaceClientController {
    @Resource
    private AirspaceClient airspaceClient;

    @GetMapping("/test/airspace")
    public void test() {
        List<NoFlyZoneResponseDTO> airspaceResponseDTOList = airspaceClient.getNoFlyZones(new NoFlyZoneQueryDTO());

        log.info("airspaceResponseDTOList:{}", airspaceResponseDTOList);
    }
}
