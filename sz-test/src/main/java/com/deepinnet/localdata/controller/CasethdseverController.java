package com.deepinnet.localdata.controller;

// import com.deepinnet.digitaltwin.common.response.Result;
//import com.deepinnet.localdata.integration.CaseReportClient;
//import com.deepinnet.localdata.integration.EventReportItemsClient;
//import com.deepinnet.localdata.integration.FileUploadClient;
//import com.deepinnet.localdata.integration.OAuth2Client;
//import com.deepinnet.localdata.integration.model.input.CaseReportRequestDTO;
//import com.deepinnet.localdata.integration.model.input.OAuth2TokenRequestDTO;
//import com.deepinnet.localdata.integration.model.output.CaseReportResponseDTO;
//import com.deepinnet.localdata.integration.model.output.CaseQueryResponseDTO;
//import com.deepinnet.localdata.integration.model.output.CaseFlowResponseDTO;
//import com.deepinnet.localdata.integration.model.output.EventReportItemsResponseDTO;
//import com.deepinnet.localdata.integration.model.output.FileUploadResponseDTO;
//import com.deepinnet.localdata.integration.model.output.OAuth2TokenResponseDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Map;

@Api(tags = "案件事项清单接口")
@RestController
@Slf4j
public class CasethdseverController {

//    @Resource
//    private EventReportItemsClient eventReportItemsClient;
//
//    @Resource
//    private OAuth2Client oAuth2Client;
//
//    @Resource
//    private FileUploadClient fileUploadClient;
//
//    @Resource
//    private CaseReportClient caseReportClient;
//
//    /**
//     * 获取事项清单
//     */
//    @ApiOperation(value = "获取事项清单", notes = "获取案件事项清单接口")
//    @GetMapping("/casethdsever/getitems")
//    public Object getItems() {
//        try {
//            log.info("开始调用获取事项清单接口");
//
//            // 调用业务逻辑获取事项清单
//            EventReportItemsResponseDTO response = eventReportItemsClient.getItems();
//
//            log.info("成功获取事项清单数据");
//
//            // 根据接口文档，需要返回原始的JSON结构
//            return response;
//
//        } catch (Exception e) {
//            log.error("获取事项清单失败", e);
//            // 返回失败响应
//            return Map.of("error", "GET_ITEMS_ERROR", "message", "获取事项清单错误");
//        }
//    }
//
//    /**
//     * 工单上报
//     */
//    @ApiOperation(value = "工单上报", notes = "工单上报接口")
//    @PostMapping(value = "/casethdsever/casereport", consumes = MediaType.APPLICATION_JSON_VALUE)
//    public CaseReportResponseDTO caseReport(
//            @RequestBody @ApiParam(value = "工单上报请求参数", required = true) CaseReportRequestDTO request) {
//        try {
//            log.info("开始调用工单上报接口: rqstname={}, rqstcontent={}, linknumber={}",
//                    request.getRqstname(), request.getRqstcontent(), request.getLinknumber());
//
//            // 调用业务逻辑处理工单上报
//            CaseReportResponseDTO response = caseReportClient.reportCase(request);
//
//            if ("1".equals(response.getStatus().getCode())) {
//                log.info("工单上报成功: casecode={}, rqstname={}",
//                        response.getCustom().getCasecode(), request.getRqstname());
//            } else {
//                log.warn("工单上报失败: rqstname={}, error={}",
//                        request.getRqstname(), response.getStatus().getText());
//            }
//
//            return response;
//
//        } catch (Exception e) {
//            log.error("工单上报过程中发生异常: rqstname={}", request.getRqstname(), e);
//            return CaseReportResponseDTO.error("工单上报失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 工单查询
//     */
//    @ApiOperation(value = "工单查询", notes = "根据工单编码查询工单详情")
//    @GetMapping("/casethdsever/getcase")
//    public CaseQueryResponseDTO getCase(
//            @RequestParam @ApiParam(value = "工单编码", required = true) String casecode) {
//        try {
//            log.info("开始调用工单查询接口: casecode={}", casecode);
//
//            // 调用业务逻辑处理工单查询
//            CaseQueryResponseDTO response = caseReportClient.queryCase(casecode);
//
//            if ("1".equals(response.getStatus().getCode())) {
//                log.info("工单查询成功: casecode={}", casecode);
//            } else {
//                log.warn("工单查询失败: casecode={}, error={}",
//                        casecode, response.getStatus().getText());
//            }
//
//            return response;
//
//        } catch (Exception e) {
//            log.error("工单查询过程中发生异常: casecode={}", casecode, e);
//            return CaseQueryResponseDTO.error("工单查询失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 工单流程查询
//     */
//    @ApiOperation(value = "工单流程查询", notes = "根据工单编码查询工单处理流程")
//    @PostMapping(value = "/casethdsever/getcaseflow", consumes = MediaType.APPLICATION_JSON_VALUE)
//    public CaseFlowResponseDTO getCaseFlow(
//            @RequestBody @ApiParam(value = "包含工单编码的JSON对象", required = true) Map<String, String> request) {
//        try {
//            String casecode = request.get("casecode");
//            log.info("开始调用工单流程查询接口: casecode={}", casecode);
//
//            // 调用业务逻辑处理工单流程查询
//            CaseFlowResponseDTO response = caseReportClient.queryCaseFlow(casecode);
//
//            if ("1".equals(response.getStatus().getCode())) {
//                log.info("工单流程查询成功: casecode={}", casecode);
//            } else {
//                log.warn("工单流程查询失败: casecode={}, error={}",
//                        casecode, response.getStatus().getText());
//            }
//
//            return response;
//
//        } catch (Exception e) {
//            String casecode = request != null ? request.get("casecode") : "unknown";
//            log.error("工单流程查询过程中发生异常: casecode={}", casecode, e);
//            return CaseFlowResponseDTO.error("工单流程查询失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * OAuth2 获取访问令牌
//     */
//    @ApiOperation(value = "获取OAuth2访问令牌", notes = "使用客户端凭证获取访问令牌")
//    @PostMapping(value = "/oauth2/token", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
//    public OAuth2TokenResponseDTO getToken(
//            @RequestParam @ApiParam(value = "授权类型", required = true) String grant_type,
//            @RequestParam @ApiParam(value = "客户端ID", required = true) String client_id,
//            @RequestParam @ApiParam(value = "客户端密钥", required = true) String client_secret) {
//
//        try {
//            log.info("开始调用OAuth2 token获取接口: clientId={}, grantType={}", client_id, grant_type);
//
//            // 构建请求对象
//            OAuth2TokenRequestDTO request = new OAuth2TokenRequestDTO();
//            request.setGrantType(grant_type);
//            request.setClientId(client_id);
//            request.setClientSecret(client_secret);
//
//            // 调用业务逻辑获取token
//            OAuth2TokenResponseDTO response = oAuth2Client.getToken(request);
//
//            if ("1".equals(response.getStatus().getCode())) {
//                log.info("OAuth2 token获取成功: clientId={}", client_id);
//            } else {
//                log.warn("OAuth2 token获取失败: clientId={}", client_id);
//            }
//
//            return response;
//
//        } catch (Exception e) {
//            log.error("OAuth2 token获取过程中发生异常", e);
//            return OAuth2TokenResponseDTO.error("0");
//        }
//    }
//
//    /**
//     * 测试接口 - 验证token
//     */
//    @ApiOperation(value = "验证OAuth2令牌", notes = "验证访问令牌是否有效")
//    @GetMapping("/oauth2/validate")
//    public Map<String, Object> validateToken(
//            @RequestParam @ApiParam(value = "访问令牌", required = true) String access_token) {
//        try {
//            log.info("开始调用OAuth2 token验证接口");
//
//            // 调用业务逻辑验证token
//            boolean isValid = ((com.deepinnet.localdata.integration.impl.OAuth2ClientImpl) oAuth2Client)
//                    .validateToken(access_token);
//            String clientId = ((com.deepinnet.localdata.integration.impl.OAuth2ClientImpl) oAuth2Client)
//                    .getClientIdByToken(access_token);
//
//            log.info("OAuth2 token验证结果: valid={}, clientId={}", isValid, clientId);
//
//            return Map.of(
//                    "valid", isValid,
//                    "client_id", clientId != null ? clientId : "",
//                    "message", isValid ? "token有效" : "token无效或已过期");
//
//        } catch (Exception e) {
//            log.error("验证OAuth2 token过程中发生异常", e);
//            return Map.of("valid", false, "client_id", "", "message", "系统内部错误");
//        }
//    }
//
//    /**
//     * 文件上传接口
//     */
//    @ApiOperation(value = "文件上传", notes = "上传文件接口")
//    @PostMapping(value = "/casethdsever/fileupload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
//    public FileUploadResponseDTO fileUpload(
//            @RequestParam("file") @ApiParam(value = "上传的文件", required = true) MultipartFile file,
//            @RequestParam("filename") @ApiParam(value = "文件名", required = true) String filename,
//            @RequestParam("clienttag") @ApiParam(value = "客户端标识", required = true) String clienttag,
//            @RequestHeader("clientid") @ApiParam(value = "应用ID", required = true) String clientid) {
//
//        try {
//            log.info("开始调用文件上传接口: filename={}, clienttag={}, clientid={}, fileSize={}",
//                    filename, clienttag, clientid, file != null ? file.getSize() : 0);
//
//            // 先验证文件
//            if (file == null || file.isEmpty()) {
//                log.warn("上传文件为空");
//                return FileUploadResponseDTO.error("上传文件不能为空");
//            }
//
//            // 调用业务逻辑上传文件
//            FileUploadResponseDTO response = fileUploadClient.uploadFile(file, filename, clienttag, clientid);
//
//            // 如果业务逻辑返回成功，实际保存文件
//            if ("1".equals(response.getStatus().getCode())) {
//                try {
//                    // 创建上传目录
//                    String uploadPath = "./uploads";
//                    Path uploadDir = Paths.get(uploadPath);
//                    if (!Files.exists(uploadDir)) {
//                        Files.createDirectories(uploadDir);
//                    }
//
//                    // 获取文件扩展名
//                    String fileExtension = "";
//                    if (filename.lastIndexOf(".") > 0) {
//                        fileExtension = filename.substring(filename.lastIndexOf("."));
//                    }
//
//                    // 使用返回的attachguid作为文件名
//                    String attachGuid = response.getCustom().getAttachguid();
//                    String savedFilename = attachGuid + fileExtension;
//                    Path filePath = uploadDir.resolve(savedFilename);
//
//                    // 保存文件
//                    file.transferTo(filePath.toFile());
//
//                    log.info("文件上传成功: filename={}, attachguid={}, savedPath={}, fileSize={}",
//                            filename, attachGuid, filePath.toAbsolutePath(), file.getSize());
//                } catch (IOException e) {
//                    log.error("文件保存失败: filename={}, error={}", filename, e.getMessage(), e);
//                    return FileUploadResponseDTO.error("文件保存失败: " + e.getMessage());
//                }
//            } else {
//                log.warn("文件上传失败: filename={}, error={}", filename, response.getStatus().getText());
//            }
//
//            return response;
//
//        } catch (Exception e) {
//            log.error("文件上传过程中发生异常: filename={}", filename, e);
//            return FileUploadResponseDTO.error("文件上传失败: " + e.getMessage());
//        }
//    }
}
