package com.deepinnet.localdata.controller;

import com.deepinnet.localdata.integration.ShipDataClient;
import com.deepinnet.localdata.integration.model.input.BaseQueryDTO;
import com.deepinnet.localdata.integration.model.output.ShipDataResponseDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * Description: 船舶数据接口测试控制器
 * 使用通用响应结构的示例
 * Date: 2025/9/8
 * Author: qoder
 */
@Api(tags = "船舶数据接口")
@RestController
@RequestMapping("/ship-data")
@Slf4j
public class ShipDataController {

    @Resource
    private ShipDataClient shipDataClient;

    /**
     * 获取船舶数据（默认参数）
     */
    @ApiOperation(value = "获取船舶数据（默认）", notes = "使用默认参数获取船舶数据JSON数据")
    @GetMapping("/data")
    public ShipDataResponseDTO getData() {
        try {
            log.info("开始调用大鹏接口获取船舶数据（默认参数）");

            ShipDataResponseDTO response = shipDataClient.getShipData();

            log.info("成功获取船舶数据，总记录数: {}", response.getTotalRecords());
            return response;

        } catch (Exception e) {
            log.error("获取船舶数据失败", e);
            throw e;
        }
    }

    /**
     * 获取船舶数据（分页查询）
     */
    @ApiOperation(value = "获取船舶数据（分页）", notes = "使用指定分页参数获取船舶数据JSON数据")
    @GetMapping("/data/paged")
    public ShipDataResponseDTO getDataPaged(
            @ApiParam(value = "页号，默认为1", example = "1") @RequestParam(defaultValue = "1") String pageNo,
            @ApiParam(value = "页面大小，默认为10，最大100", example = "10") @RequestParam(defaultValue = "10") String pageSize,
            @ApiParam(value = "搜索条件，JSON字符串格式", example = "{}") @RequestParam(defaultValue = "") String search) {
        try {
            log.info("开始调用大鹏接口获取船舶数据（分页查询），页号: {}, 页面大小: {}, 搜索条件: {}", pageNo, pageSize, search);

            ShipDataResponseDTO response = shipDataClient.getShipData(pageNo, pageSize, search);

            log.info("成功获取船舶数据，总记录数: {}, 当前页: {}, 页面大小: {}", 
                    response.getTotalRecords(), response.getPageNo(), response.getPageSize());
            return response;

        } catch (Exception e) {
            log.error("获取船舶数据失败", e);
            throw e;
        }
    }

    /**
     * 获取船舶数据（POST方式）
     */
    @ApiOperation(value = "获取船舶数据（POST）", notes = "使用POST方式提交查询参数获取船舶数据JSON数据")
    @PostMapping("/data")
    public ShipDataResponseDTO getDataByPost(@RequestBody BaseQueryDTO queryDTO) {
        try {
            log.info("开始调用大鹏接口获取船舶数据（POST方式），查询参数: {}", queryDTO);

            ShipDataResponseDTO response = shipDataClient.getShipData(queryDTO);

            log.info("成功获取船舶数据，总记录数: {}, 当前页: {}, 页面大小: {}", 
                    response.getTotalRecords(), response.getPageNo(), response.getPageSize());
            return response;

        } catch (Exception e) {
            log.error("获取船舶数据失败", e);
            throw e;
        }
    }

    /**
     * 测试船舶数据接口连通性
     */
    @ApiOperation(value = "测试船舶数据接口", notes = "测试船舶数据接口的连通性和基本功能")
    @GetMapping("/test")
    public String testConnection() {
        try {
            log.info("开始测试船舶数据接口连通性");

            ShipDataResponseDTO response = shipDataClient.getShipData("1", "5", "");

            if (response != null && response.isSuccess()) {
                String message = String.format("船舶数据接口测试成功！总记录数: %d, 返回数据条数: %d", 
                        response.getTotalRecords(), 
                        response.getResultList() != null ? response.getResultList().size() : 0);
                log.info(message);
                return message;
            } else {
                String message = "船舶数据接口测试失败：响应数据异常";
                log.warn(message);
                return message;
            }

        } catch (Exception e) {
            String message = "船舶数据接口测试失败：" + e.getMessage();
            log.error(message, e);
            return message;
        }
    }

    /**
     * 获取船舶数据统计信息
     */
    @ApiOperation(value = "获取船舶数据统计", notes = "获取船舶数据的统计信息")
    @GetMapping("/stats")
    public String getStats() {
        try {
            log.info("开始获取船舶数据统计信息");

            ShipDataResponseDTO response = shipDataClient.getShipData("1", "1", "");

            if (response != null && response.isSuccess()) {
                String message = String.format(
                        "船舶数据统计信息：\n" +
                        "- 总记录数: %d\n" +
                        "- 是否支持分页: %s\n" +
                        "- 当前页号: %d\n" +
                        "- 页面大小: %d",
                        response.getTotalRecords(),
                        response.getIsPageable(),
                        response.getPageNo(),
                        response.getPageSize()
                );
                log.info("船舶数据统计信息获取成功");
                return message;
            } else {
                String message = "获取船舶数据统计信息失败：响应数据异常";
                log.warn(message);
                return message;
            }

        } catch (Exception e) {
            String message = "获取船舶数据统计信息失败：" + e.getMessage();
            log.error(message, e);
            return message;
        }
    }
}
