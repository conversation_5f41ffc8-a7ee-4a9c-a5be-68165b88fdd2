package com.deepinnet.localdata.controller;

import com.deepinnet.localdata.integration.ParameterSettingsClient;
import com.deepinnet.localdata.integration.model.input.ParameterSettingsQueryDTO;
import com.deepinnet.localdata.integration.model.output.ParameterSettingsResponseDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * Description: 参数设置信息接口测试控制器
 * Date: 2025/8/27
 * Author: qoder
 */
@Api(tags = "参数设置信息接口")
@RestController
@RequestMapping("/parameter-settings")
@Slf4j
public class ParameterSettingsController {

    @Resource
    private ParameterSettingsClient parameterSettingsClient;

    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 获取参数设置信息（默认参数）
     */
    @ApiOperation(value = "获取参数设置信息（默认）", notes = "使用默认参数获取参数设置信息JSON数据")
    @GetMapping("/data")
    public ParameterSettingsResponseDTO getData() {
        try {
            log.info("开始调用大鹏接口获取参数设置信息（默认参数）");

            ParameterSettingsResponseDTO response = parameterSettingsClient.getParameterSettingsData();

            log.info("成功获取参数设置信息");
            return response;

        } catch (Exception e) {
            log.error("获取参数设置信息失败", e);
            throw e;
        }
    }

    /**
     * 获取参数设置信息（带参数）
     */
    @ApiOperation(value = "获取参数设置信息（带参数）", notes = "使用指定参数获取参数设置信息JSON数据")
    @GetMapping("/data/paged")
    public ParameterSettingsResponseDTO getDataPaged(
            @RequestParam(defaultValue = "1") @ApiParam(value = "页号，默认为1") String pageNo,
            @RequestParam(defaultValue = "10") @ApiParam(value = "页面大小，默认为10，最大100") String pageSize,
            @RequestParam(defaultValue = "") @ApiParam(value = "搜索条件JSON字符串，为空传\"\"") String search,
            @RequestParam(required = false) @ApiParam(value = "token参数（可选）") String token) {
        try {
            log.info("开始调用大鹏接口获取参数设置信息，参数: pageNo={}, pageSize={}, search={}, token={}",
                    pageNo, pageSize, search, token);

            ParameterSettingsQueryDTO queryDTO = new ParameterSettingsQueryDTO();
            queryDTO.setPageNo(pageNo);
            queryDTO.setPageSize(pageSize);
            queryDTO.setSearch(search);
            queryDTO.setToken(token);

            ParameterSettingsResponseDTO response = parameterSettingsClient.getParameterSettingsData(queryDTO);

            log.info("成功获取参数设置信息");
            return response;

        } catch (Exception e) {
            log.error("获取参数设置信息失败，参数: pageNo={}, pageSize={}, search={}", pageNo, pageSize, search, e);
            throw e;
        }
    }

    /**
     * 获取参数设置信息（POST方式）
     */
    @ApiOperation(value = "获取参数设置信息（POST）", notes = "使用POST方式提交查询参数获取参数设置信息JSON数据")
    @PostMapping("/data")
    public ParameterSettingsResponseDTO getDataByPost(
            @RequestBody @ApiParam(value = "查询参数", required = true) ParameterSettingsQueryDTO queryDTO) {
        try {
            log.info("开始调用大鹏接口获取参数设置信息（POST），参数: {}", queryDTO);

            ParameterSettingsResponseDTO response = parameterSettingsClient.getParameterSettingsData(queryDTO);

            log.info("成功获取参数设置信息");
            return response;

        } catch (Exception e) {
            log.error("获取参数设置信息失败，参数: {}", queryDTO, e);
            throw e;
        }
    }

    /**
     * 测试参数设置信息接口连通性
     */
    @ApiOperation(value = "测试参数设置信息接口", notes = "测试参数设置信息接口的连通性和基本功能")
    @GetMapping("/test")
    public Map<String, Object> testConnection() {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("开始测试大鹏参数设置信息接口连通性");

            // 测试基本连接
            long startTime = System.currentTimeMillis();
            ParameterSettingsResponseDTO response = parameterSettingsClient.getParameterSettingsData();
            long endTime = System.currentTimeMillis();

            result.put("success", true);
            result.put("response_time_ms", endTime - startTime);
            result.put("has_response", response != null);

            if (response != null && response.getGetDataJsonResponse() != null) {
                // 注意：returnInfo是字符串类型，不是ReturnInfo对象
                String returnInfoStr = response.getGetDataJsonResponse().getReturnInfo();
                if (returnInfoStr != null) {
                    result.put("return_info", returnInfoStr);
                    // 解析JSON字符串来获取详细信息
                    try {
                        JsonNode returnInfoJson = objectMapper.readTree(returnInfoStr);
                        if (returnInfoJson.has("code")) {
                            result.put("return_code", returnInfoJson.get("code").asInt());
                        }
                        if (returnInfoJson.has("message")) {
                            result.put("return_message", returnInfoJson.get("message").asText());
                        }
                        if (returnInfoJson.has("hasSearch")) {
                            result.put("has_search_support", returnInfoJson.get("hasSearch").asBoolean());
                        }
                    } catch (Exception jsonEx) {
                        log.warn("Failed to parse return info as JSON: {}", returnInfoStr, jsonEx);
                        result.put("return_info_parse_error", "Failed to parse return info as JSON");
                    }
                }

                ParameterSettingsResponseDTO.DataInfo dataInfo = response.getGetDataJsonResponse().getData();
                if (dataInfo != null) {
                    result.put("total_records", dataInfo.getTotalRecords());
                    result.put("error_flag", dataInfo.getErrorFlag());
                    result.put("result_count", dataInfo.getResult() != null ? dataInfo.getResult().size() : 0);
                }
            }

            log.info("大鹏参数设置信息接口测试成功");

        } catch (Exception e) {
            log.error("大鹏参数设置信息接口测试失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }

        return result;
    }
}