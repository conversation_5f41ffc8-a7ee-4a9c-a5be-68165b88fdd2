package com.deepinnet.localdata.controller;

import com.deepinnet.localdata.integration.DaPengTokenClient;
import com.deepinnet.localdata.integration.model.input.OAuth2TokenRequestDTO;
import com.deepinnet.localdata.integration.model.output.OAuth2TokenResponseDTO;
import com.deepinnet.localdata.integration.service.DaPengTokenService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 大鹏Token管理测试控制器
 * 支持分离Token服务器和数据服务器
 * Token服务器：************:30430
 * 数据服务器：************:8280
 * Date: 2025/9/1
 * Author: qoder
 */
@Api(tags = "大鹏Token管理接口")
@RestController
@RequestMapping("/dapeng-token")
@Slf4j
public class DaPengTokenController {

    @Resource
    private DaPengTokenClient tokenClient;

    @Resource
    private DaPengTokenService tokenService;

    /**
     * 获取大鹏Token（使用默认配置）
     */
    @ApiOperation(value = "获取大鹏Token（默认）", notes = "使用默认配置获取大鹏访问令牌")
    @GetMapping("/get")
    public OAuth2TokenResponseDTO getToken() {
        try {
            log.info("开始获取大鹏Token（默认配置）");
            OAuth2TokenResponseDTO response = tokenClient.getDaPengToken();
            log.info("大鹏Token获取完成");
            return response;
        } catch (Exception e) {
            log.error("获取大鹏Token失败", e);
            throw e;
        }
    }

    /**
     * 获取大鹏Token（用户名密码方式）
     */
    @ApiOperation(value = "获取大鹏Token（用户名密码）", notes = "使用用户名密码获取大鹏访问令牌")
    @GetMapping("/get-by-username")
    public OAuth2TokenResponseDTO getTokenByUsername(
            @RequestParam @ApiParam(value = "用户名", required = true) String userName,
            @RequestParam @ApiParam(value = "密码", required = true) String password) {
        try {
            log.info("开始获取大鹏Token（用户名密码方式），userName: {}", userName);

            // 调用实现类的用户名密码方法
            com.deepinnet.localdata.integration.impl.DaPengTokenClientImpl tokenClientImpl = (com.deepinnet.localdata.integration.impl.DaPengTokenClientImpl) tokenClient;
            OAuth2TokenResponseDTO response = tokenClientImpl.getDaPengTokenByUserPassword(userName, password);

            log.info("大鹏Token获取完成");
            return response;
        } catch (Exception e) {
            log.error("获取大鹏Token失败", e);
            throw e;
        }
    }

    /**
     * 获取大鹏Token（使用配置凭证）
     */
    @ApiOperation(value = "获取大鹏Token（配置凭证）", notes = "使用application.yml中配置的用户名密码获取大鹏访问令牌，支持MD5预设密码")
    @GetMapping("/get-by-config")
    public OAuth2TokenResponseDTO getTokenByConfig() {
        try {
            log.info("开始获取大鹏Token（配置凭证方式）");

            // 调用实现类的配置凭证方法
            com.deepinnet.localdata.integration.impl.DaPengTokenClientImpl tokenClientImpl = (com.deepinnet.localdata.integration.impl.DaPengTokenClientImpl) tokenClient;
            OAuth2TokenResponseDTO response = tokenClientImpl.getDaPengTokenWithConfigCredentials();

            log.info("大鹏Token获取完成");
            return response;
        } catch (Exception e) {
            log.error("获取大鹏Token失败", e);
            throw e;
        }
    }

    @ApiOperation(value = "获取大鹏Token（自定义）", notes = "使用自定义参数获取大鹏访问令牌")
    @PostMapping("/get")
    public OAuth2TokenResponseDTO getTokenWithParams(
            @RequestBody @ApiParam(value = "Token请求参数", required = true) OAuth2TokenRequestDTO request) {
        try {
            log.info("开始获取大鹏Token（自定义参数），clientId: {}", request.getClientId());
            OAuth2TokenResponseDTO response = tokenClient.getDaPengToken(request);
            log.info("大鹏Token获取完成");
            return response;
        } catch (Exception e) {
            log.error("获取大鹏Token失败", e);
            throw e;
        }
    }

    /**
     * 刷新Token
     */
    @ApiOperation(value = "刷新Token", notes = "手动刷新大鹏访问令牌")
    @PostMapping("/refresh")
    public Map<String, Object> refreshToken() {
        Map<String, Object> result = new HashMap<>();
        try {
            log.info("开始刷新大鹏Token");
            boolean success = tokenService.refreshToken();

            result.put("success", success);
            result.put("message", success ? "Token刷新成功" : "Token刷新失败");

            log.info("大鹏Token刷新完成，结果: {}", success);
            return result;
        } catch (Exception e) {
            log.error("刷新大鹏Token失败", e);
            result.put("success", false);
            result.put("message", "Token刷新异常: " + e.getMessage());
            return result;
        }
    }

    /**
     * 测试URL中Token替换功能
     */
    @ApiOperation(value = "测试Token替换", notes = "测试URL中硬编码Token的动态替换功能")
    @GetMapping("/test-replace")
    public Map<String, Object> testTokenReplace(
            @RequestParam @ApiParam(value = "测试URL", required = true) String testUrl) {
        Map<String, Object> result = new HashMap<>();
        try {
            log.info("开始测试Token替换，原始URL: {}", testUrl);

            // 提取原始Token
            String originalToken = tokenService.extractTokenFromUrl(testUrl);

            // 执行Token替换
            String newUrl = tokenService.replaceTokenInUrl(testUrl);

            // 提取新Token
            String newToken = tokenService.extractTokenFromUrl(newUrl);

            result.put("success", true);
            result.put("originalUrl", testUrl);
            result.put("newUrl", newUrl);
            result.put("originalToken", originalToken);
            result.put("newToken", newToken);
            result.put("isReplaced", !testUrl.equals(newUrl));

            log.info("Token替换测试完成");
            return result;
        } catch (Exception e) {
            log.error("Token替换测试失败", e);
            result.put("success", false);
            result.put("message", "测试异常: " + e.getMessage());
            return result;
        }
    }

    /**
     * 检查Token状态
     */
    @ApiOperation(value = "检查Token状态", notes = "检查当前Token的有效性和状态信息")
    @GetMapping("/status")
    public Map<String, Object> checkTokenStatus() {
        Map<String, Object> result = new HashMap<>();
        try {
            log.info("开始检查Token状态");

            boolean needRefresh = tokenService.needRefreshToken();

            result.put("success", true);
            result.put("needRefresh", needRefresh);
            result.put("status", needRefresh ? "Token需要刷新" : "Token有效");

            log.info("Token状态检查完成");
            return result;
        } catch (Exception e) {
            log.error("Token状态检查失败", e);
            result.put("success", false);
            result.put("message", "检查异常: " + e.getMessage());
            return result;
        }
    }
}
