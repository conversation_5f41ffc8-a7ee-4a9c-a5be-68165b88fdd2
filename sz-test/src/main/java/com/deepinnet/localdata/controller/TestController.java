package com.deepinnet.localdata.controller;

//import com.deepinnet.localdata.integration.OAuth2Client;
//import com.deepinnet.localdata.integration.model.input.OAuth2TokenRequestDTO;
//import com.deepinnet.localdata.integration.model.output.OAuth2TokenResponseDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 测试控制器
 */
@Api(tags = "测试接口")
@RestController
@RequestMapping("/test")
@Slf4j
public class TestController {

//    @Resource
//    private OAuth2Client oAuth2Client;

    /**
     * 测试OAuth2功能的综合接口
     */
//    @ApiOperation(value = "测试OAuth2功能", notes = "执行OAuth2功能的综合测试")
//    @GetMapping("/oauth2")
//    public Map<String, Object> testOAuth2() {
//        log.info("开始执行OAuth2功能测试");
//
//        Map<String, Object> result = new HashMap<>();
//        List<Map<String, Object>> testResults = new ArrayList<>();
//
//        // 测试用例1：正确的客户端凭证
//        Map<String, Object> test1 = testValidCredentials();
//        testResults.add(test1);
//
//        // 测试用例2：错误的客户端凭证
//        Map<String, Object> test2 = testInvalidCredentials();
//        testResults.add(test2);
//
//        // 测试用例3：错误的授权类型
//        Map<String, Object> test3 = testInvalidGrantType();
//        testResults.add(test3);
//
//        // 测试用例4：所有预设客户端
//        Map<String, Object> test4 = testAllClients();
//        testResults.add(test4);
//
//        // 统计测试结果
//        long passedCount = testResults.stream()
//                .mapToLong(test -> (Boolean) test.get("passed") ? 1 : 0)
//                .sum();
//
//        result.put("total_tests", testResults.size());
//        result.put("passed_tests", passedCount);
//        result.put("failed_tests", testResults.size() - passedCount);
//        result.put("success_rate", String.format("%.2f%%", (double) passedCount / testResults.size() * 100));
//        result.put("test_details", testResults);
//
//        log.info("OAuth2功能测试完成: 总数={}, 通过={}, 失败={}",
//                testResults.size(), passedCount, testResults.size() - passedCount);
//
//        return result;
//    }
//
//    private Map<String, Object> testValidCredentials() {
//        Map<String, Object> result = new HashMap<>();
//        result.put("test_name", "正确客户端凭证测试");
//
////        try {
////            OAuth2TokenRequestDTO request = new OAuth2TokenRequestDTO();
////            request.setGrantType("client_credentials");
////            request.setClientId("test_client_id");
////            request.setClientSecret("test_client_secret");
////
////            OAuth2TokenResponseDTO response = oAuth2Client.getToken(request);
////
////            boolean passed = "1".equals(response.getStatus().getCode())
////                    && response.getCustom() != null
////                    && response.getCustom().getAccessToken() != null;
////
////            result.put("passed", passed);
////            result.put("response_code", response.getStatus().getCode());
////            result.put("has_token", response.getCustom() != null && response.getCustom().getAccessToken() != null);
////            result.put("expires_in", response.getCustom() != null ? response.getCustom().getExpiresIn() : null);
////
////        } catch (Exception e) {
////            result.put("passed", false);
////            result.put("error", e.getMessage());
////        }
//
//        return result;
//    }
//
//    private Map<String, Object> testInvalidCredentials() {
//        Map<String, Object> result = new HashMap<>();
//        result.put("test_name", "错误客户端凭证测试");
//
//        try {
//            OAuth2TokenRequestDTO request = new OAuth2TokenRequestDTO();
//            request.setGrantType("client_credentials");
//            request.setClientId("wrong_client");
//            request.setClientSecret("wrong_secret");
//
//            OAuth2TokenResponseDTO response = oAuth2Client.getToken(request);
//
//            boolean passed = "0".equals(response.getStatus().getCode())
//                    && response.getCustom() == null;
//
//            result.put("passed", passed);
//            result.put("response_code", response.getStatus().getCode());
//            result.put("has_token", response.getCustom() != null);
//
//        } catch (Exception e) {
//            result.put("passed", false);
//            result.put("error", e.getMessage());
//        }
//
//        return result;
//    }
//
//    private Map<String, Object> testInvalidGrantType() {
//        Map<String, Object> result = new HashMap<>();
//        result.put("test_name", "错误授权类型测试");
//
//        try {
//            OAuth2TokenRequestDTO request = new OAuth2TokenRequestDTO();
//            request.setGrantType("authorization_code");
//            request.setClientId("test_client_id");
//            request.setClientSecret("test_client_secret");
//
//            OAuth2TokenResponseDTO response = oAuth2Client.getToken(request);
//
//            boolean passed = "0".equals(response.getStatus().getCode());
//
//            result.put("passed", passed);
//            result.put("response_code", response.getStatus().getCode());
//            result.put("grant_type", "authorization_code");
//
//        } catch (Exception e) {
//            result.put("passed", false);
//            result.put("error", e.getMessage());
//        }
//
//        return result;
//    }
//
//    private Map<String, Object> testAllClients() {
//        Map<String, Object> result = new HashMap<>();
//        result.put("test_name", "所有预设客户端测试");
//
//        try {
//            String[] clientIds = { "test_client_id", "demo_client", "app_client" };
//            String[] clientSecrets = { "test_client_secret", "demo_secret_123", "app_secret_456" };
//
//            List<Map<String, Object>> clientResults = new ArrayList<>();
//            int successCount = 0;
//
//            for (int i = 0; i < clientIds.length; i++) {
//                Map<String, Object> clientResult = new HashMap<>();
//                clientResult.put("client_id", clientIds[i]);
//
//                OAuth2TokenRequestDTO request = new OAuth2TokenRequestDTO();
//                request.setGrantType("client_credentials");
//                request.setClientId(clientIds[i]);
//                request.setClientSecret(clientSecrets[i]);
//
//                OAuth2TokenResponseDTO response = oAuth2Client.getToken(request);
//
//                boolean success = "1".equals(response.getStatus().getCode())
//                        && response.getCustom() != null
//                        && response.getCustom().getAccessToken() != null;
//
//                clientResult.put("success", success);
//                clientResult.put("response_code", response.getStatus().getCode());
//                clientResult.put("has_token",
//                        response.getCustom() != null && response.getCustom().getAccessToken() != null);
//
//                clientResults.add(clientResult);
//                if (success)
//                    successCount++;
//            }
//
//            result.put("passed", successCount == clientIds.length);
//            result.put("success_count", successCount);
//            result.put("total_clients", clientIds.length);
//            result.put("client_results", clientResults);
//
//        } catch (Exception e) {
//            result.put("passed", false);
//            result.put("error", e.getMessage());
//        }
//
//        return result;
//    }
//
//    /**
//     * 获取系统状态
//     */
//    @ApiOperation(value = "获取系统状态", notes = "获取系统运行状态信息")
//    @GetMapping("/status")
//    public Map<String, Object> getSystemStatus() {
//        Map<String, Object> status = new HashMap<>();
//        status.put("service", "local-data-adapter");
//        status.put("status", "running");
//        status.put("timestamp", System.currentTimeMillis());
//        status.put("oauth2_client_available", oAuth2Client != null);
//
//        return status;
//    }
}
