package com.deepinnet.localdata.controller;

import com.deepinnet.localdata.integration.BoatRescueAlarmClient;
import com.deepinnet.localdata.integration.model.input.BoatRescueAlarmQueryDTO;
import com.deepinnet.localdata.integration.model.output.BoatRescueAlarmResponseDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * Description: 船舶求助报警信息接口测试控制器
 * Date: 2025/8/27
 * Author: qoder
 */
@Api(tags = "船舶求助报警信息接口")
@RestController
@RequestMapping("/boat-rescue-alarm")
@Slf4j
public class BoatRescueAlarmController {

    @Resource
    private BoatRescueAlarmClient boatRescueAlarmClient;

    /**
     * 获取船舶求助报警信息（默认参数）
     */
    @ApiOperation(value = "获取船舶求助报警信息（默认）", notes = "使用默认参数获取船舶求助报警信息JSON数据")
    @GetMapping("/data")
    public BoatRescueAlarmResponseDTO getData() {
        try {
            log.info("开始调用大鹏接口获取船舶求助报警信息（默认参数）");

            BoatRescueAlarmResponseDTO response = boatRescueAlarmClient.getBoatRescueAlarmData();

            log.info("成功获取船舶求助报警信息");
            return response;

        } catch (Exception e) {
            log.error("获取船舶求助报警信息失败", e);
            throw e;
        }
    }

    /**
     * 获取船舶求助报警信息（带参数）
     */
    @ApiOperation(value = "获取船舶求助报警信息（带参数）", notes = "使用指定参数获取船舶求助报警信息JSON数据")
    @GetMapping("/data/paged")
    public BoatRescueAlarmResponseDTO getDataPaged(
            @RequestParam(defaultValue = "1") @ApiParam(value = "页号，默认为1") String pageNo,
            @RequestParam(defaultValue = "10") @ApiParam(value = "页面大小，默认为10，最大100") String pageSize,
            @RequestParam(defaultValue = "") @ApiParam(value = "搜索条件JSON字符串，为空传\"\"") String search,
            @RequestParam(required = false) @ApiParam(value = "token参数（可选）") String token) {
        try {
            log.info("开始调用大鹏接口获取船舶求助报警信息，参数: pageNo={}, pageSize={}, search={}, token={}",
                    pageNo, pageSize, search, token);

            BoatRescueAlarmQueryDTO queryDTO = new BoatRescueAlarmQueryDTO();
            queryDTO.setPageNo(pageNo);
            queryDTO.setPageSize(pageSize);
            queryDTO.setSearch(search);
            queryDTO.setToken(token);

            BoatRescueAlarmResponseDTO response = boatRescueAlarmClient.getBoatRescueAlarmData(queryDTO);

            log.info("成功获取船舶求助报警信息");
            return response;

        } catch (Exception e) {
            log.error("获取船舶求助报警信息失败，参数: pageNo={}, pageSize={}, search={}", pageNo, pageSize, search, e);
            throw e;
        }
    }

    /**
     * 获取船舶求助报警信息（POST方式）
     */
    @ApiOperation(value = "获取船舶求助报警信息（POST）", notes = "使用POST方式提交查询参数获取船舶求助报警信息JSON数据")
    @PostMapping("/data")
    public BoatRescueAlarmResponseDTO getDataByPost(
            @RequestBody @ApiParam(value = "查询参数", required = true) BoatRescueAlarmQueryDTO queryDTO) {
        try {
            log.info("开始调用大鹏接口获取船舶求助报警信息（POST），参数: {}", queryDTO);

            BoatRescueAlarmResponseDTO response = boatRescueAlarmClient.getBoatRescueAlarmData(queryDTO);

            log.info("成功获取船舶求助报警信息");
            return response;

        } catch (Exception e) {
            log.error("获取船舶求助报警信息失败，参数: {}", queryDTO, e);
            throw e;
        }
    }

    /**
     * 测试船舶求助报警信息接口连通性
     */
    @ApiOperation(value = "测试船舶求助报警信息接口", notes = "测试船舶求助报警信息接口的连通性和基本功能")
    @GetMapping("/test")
    public Map<String, Object> testConnection() {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("开始测试大鹏船舶求助报警信息接口连通性");

            // 测试基本连接
            long startTime = System.currentTimeMillis();
            BoatRescueAlarmResponseDTO response = boatRescueAlarmClient.getBoatRescueAlarmData();
            long endTime = System.currentTimeMillis();

            result.put("success", true);
            result.put("response_time_ms", endTime - startTime);
            result.put("has_response", response != null);

            if (response != null && response.getGetDataJsonResponse() != null) {
                // 注意：returnInfo是字符串类型，不是ReturnInfo对象
                String returnInfoStr = response.getGetDataJsonResponse().getReturnInfo();
                if (returnInfoStr != null) {
                    result.put("return_info", returnInfoStr);
                }

                BoatRescueAlarmResponseDTO.DataInfo dataInfo = response.getGetDataJsonResponse().getData();
                if (dataInfo != null) {
                    result.put("total_records", dataInfo.getTotalRecords());
                    result.put("error_flag", dataInfo.getErrorFlag());
                    result.put("result_count", dataInfo.getResult() != null ? dataInfo.getResult().size() : 0);
                }
            }

            log.info("大鹏船舶求助报警信息接口测试成功");

        } catch (Exception e) {
            log.error("大鹏船舶求助报警信息接口测试失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }

        return result;
    }
}