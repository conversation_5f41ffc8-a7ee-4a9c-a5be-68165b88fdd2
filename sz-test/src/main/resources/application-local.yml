server:
  port: 9900
spring:
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          url: jdbc:postgresql://*************:5432/digitaltwin
          username: <PERSON><PERSON>(sVMyCam15Oox9BCvuJvd6VqXcXdCEzzb)
          password: <PERSON><PERSON>(miyi4KUBRWgcx4NhYmMqOq3lFb20I9o+)
          driver-class-name: org.postgresql.Driver
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  servlet:
    multipart:
      max-file-size: 5000MB
      max-request-size: 5000MB
#  datasource:
#    driver-class-name: com.mysql.cj.jdbc.Driver
#    url: jdbc:mysql://*************:3306/test?useUnicode=true&characterEncoding=utf-8&useSSL=false&serverTimezone=UTC
#    username: root
#    password: 123456

mybatis-plus:
  mapper-locations: classpath:mapper/*.xml  # mapper XML文件位置
  type-aliases-package: com.example.demo.entity  # 实体类包路径
  configuration:
    map-underscore-to-camel-case: true  # 开启驼峰命名转换

knife4j:
  enable: true

logging:
  level:
    com.deepinnet.localdata.publish.biz.mapper: DEBUG  # 你的Mapper包路径
    org.springframework.jdbc.core.JdbcTemplate: DEBUG  # 可选：显示SQL参数
    org.springframework.jdbc.core.StatementCreatorUtils: DEBUG  # 可选：显示SQL参数


out-api:
  da-peng:
    server-url: http://************:8280          # 数据接口服务器地址
    token-server-url: http://************:30430   # Token获取服务器地址
    # base-path: ""                              # 基础路径
    mock-enabled: true                           # 启用模拟数据模式（开发测试用）
    # token: YOUR_TOKEN_HERE                    # 如果需要token，请在这里配置
    # client-id: YOUR_CLIENT_ID                 # OAuth2客户端ID（用于动态获取Token）
    # client-secret: YOUR_CLIENT_SECRET         # OAuth2客户端密钥（用于动态获取Token）
    user-name: 黄琳                              # 大鹏系统用户名（用于获取Token）
    password:                                    # 大鹏系统明文密码（可选，如果有pwd则不需要）
    password-md5: aa76e2db005141acc216c92496a34654  # 大鹏系统MD5加密密码（用于pwd参数）
    token-time: "86400"                         # Token有效时间（秒），默认24小时
