server:
  port: 9900
spring:
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  servlet:
    multipart:
      max-file-size: 5000MB
      max-request-size: 5000MB
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          url: ************************************************
          username: <PERSON><PERSON>(sVMyCam15Oox9BCvuJvd6VqXcXdCEzzb)
          password: E<PERSON>(miyi4KUBRWgcx4NhYmMqOq3lFb20I9o+)
          driver-class-name: org.postgresql.Driver

knife4j:
  enable: true

out-api:
  da-peng:
    server-url: http://************:8280          # 数据接口服务器地址
    token-server-url: http://************:30430   # Token获取服务器地址
    # base-path: ""                              # 基础路径
    mock-enabled: true                           # 启用模拟数据模式（开发测试用）
    # token: YOUR_TOKEN_HERE                    # 如果需要token，请在这里配置
    # client-id: YOUR_CLIENT_ID                 # OAuth2客户端ID（用于动态获取Token）
    # client-secret: YOUR_CLIENT_SECRET         # OAuth2客户端密钥（用于动态获取Token）
    user-name: dapeng_user                       # 大鹏系统用户名（用于获取Token）
    password: original_password                  # 大鹏系统明文密码（用于获取Token）
    password-md5: aa76e2db005141acc216c92496a34654  # 大鹏系统MD5加密密码（用于pwd参数）
    token-time: "86400"                         # Token有效时间（秒），默认24小时

