# 通用响应结构使用指南

## 概述

本文档说明如何使用通用响应结构 `BaseDataResponseDTO<T>` 来处理大鹏接口的标准响应格式。这种设计可以大大减少重复代码，提高开发效率。

## 核心组件

### 1. BaseDataResponseDTO<T>

通用响应基类，使用泛型来指定具体的数据记录类型。

```java
public class BaseDataResponseDTO<T> implements Serializable {
    @JsonProperty("getDataJsonResponse")
    private GetDataJsonResponse<T> getDataJsonResponse;
    
    // 便利方法
    public boolean isSuccess() { ... }
    public List<T> getResultList() { ... }
    public Integer getTotalRecords() { ... }
}
```

### 2. 数据记录类

每种数据类型需要定义对应的记录类，例如 `ShipDataRecord`：

```java
@Data
public class ShipDataRecord implements Serializable {
    @JsonProperty("shipstatus")
    private String shipStatus;
    
    @JsonProperty("shipid")
    private String shipId;
    
    // 其他字段...
}
```

### 3. 具体响应类

继承基类并指定泛型类型：

```java
public class ShipDataResponseDTO extends BaseDataResponseDTO<ShipDataRecord> {
    // 无需额外代码，继承所有基类功能
}
```

## 使用示例

### 1. 创建新的数据类型

假设要添加一个新的"车辆数据"接口：

#### 步骤1：创建数据记录类

```java
@Data
public class VehicleDataRecord implements Serializable {
    @JsonProperty("vehicleid")
    private String vehicleId;
    
    @JsonProperty("vehicletype")
    private String vehicleType;
    
    @JsonProperty("location")
    private String location;
    
    // 其他字段...
}
```

#### 步骤2：创建响应DTO

```java
public class VehicleDataResponseDTO extends BaseDataResponseDTO<VehicleDataRecord> {
    // 继承所有基类功能
}
```

#### 步骤3：创建客户端接口

```java
@GenerateDefaultClient
public interface VehicleDataClient {
    VehicleDataResponseDTO getVehicleData(BaseQueryDTO queryDTO);
    VehicleDataResponseDTO getVehicleData(String pageNo, String pageSize, String search);
    VehicleDataResponseDTO getVehicleData();
}
```

#### 步骤4：创建客户端实现

```java
@Component
public class VehicleDataClientImpl extends BaseHttpRequestClient implements VehicleDataClient {
    
    @Resource
    private DaPengTokenClientImpl daPengTokenClient;
    
    @Override
    public VehicleDataResponseDTO getVehicleData(BaseQueryDTO queryDTO) {
        // 实现逻辑，参考 ShipDataClientImpl
        // ...
        return httpRequest(param, VehicleDataResponseDTO.class, null);
    }
    
    // 其他方法实现...
}
```

### 2. 使用响应数据

```java
// 调用接口
ShipDataResponseDTO response = shipDataClient.getShipData();

// 检查是否成功
if (response.isSuccess()) {
    // 获取数据列表
    List<ShipDataRecord> ships = response.getResultList();
    
    // 获取分页信息
    Integer totalRecords = response.getTotalRecords();
    Integer pageNo = response.getPageNo();
    Integer pageSize = response.getPageSize();
    
    // 处理数据
    for (ShipDataRecord ship : ships) {
        System.out.println("船舶ID: " + ship.getShipId());
        System.out.println("船舶状态: " + ship.getShipStatus());
    }
} else {
    System.out.println("请求失败");
}
```

## 优势

### 1. 代码复用
- 所有接口共享相同的响应结构解析逻辑
- 减少重复的DTO定义
- 统一的便利方法

### 2. 类型安全
- 泛型确保编译时类型检查
- 避免类型转换错误

### 3. 易于维护
- 响应格式变更只需修改基类
- 新增接口只需定义数据记录类

### 4. 忽略无用字段
- `title` 字段被自动忽略，不进行反序列化
- 使用 `@JsonIgnoreProperties(ignoreUnknown = true)` 忽略未知字段

## 最佳实践

### 1. 字段命名
- 使用 `@JsonProperty` 注解映射JSON字段名
- Java字段名使用驼峰命名法
- 保持字段名的一致性

### 2. 数据验证
- 在客户端实现中验证查询参数
- 使用 `BaseQueryDTO.validatePagination()` 验证分页参数

### 3. 错误处理
- 使用 `response.isSuccess()` 检查请求是否成功
- 使用 `ResponseParseUtil` 工具类解析错误信息

### 4. 日志记录
- 记录关键操作的日志
- 包含请求参数和响应摘要信息

## 工具类

### ResponseParseUtil
提供响应解析的工具方法：

```java
// 解析returnInfo字符串
BaseDataResponseDTO.ReturnInfo returnInfo = ResponseParseUtil.parseReturnInfo(returnInfoStr);

// 判断是否成功
boolean success = ResponseParseUtil.isSuccess(returnInfoStr);

// 获取响应摘要
String summary = ResponseParseUtil.getResponseSummary(response);
```

### DataClientFactory
统一管理所有数据客户端：

```java
// 获取客户端
ShipDataClient client = (ShipDataClient) factory.getClient(ClientType.SHIP_DATA);

// 检查客户端状态
String statusReport = factory.getClientStatusReport();
```

## 注意事项

1. **title字段**: 由于意义不大且结构复杂，已配置为忽略反序列化
2. **Token管理**: 自动处理Token的获取和替换，无需手动管理
3. **分页限制**: 页面大小最大为100，超过会自动调整
4. **错误处理**: 网络异常和解析异常会向上抛出，需要在调用方处理

## 扩展性

这种设计支持轻松扩展：

1. **新增字段**: 在数据记录类中添加新字段即可
2. **新增接口**: 按照模板创建新的客户端和响应类
3. **自定义处理**: 可以在具体的响应类中添加特定的业务方法

通过这种通用设计，可以快速适配各种大鹏接口，提高开发效率和代码质量。
